import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { X, Trash2 } from "lucide-react";
import { type Candidate } from "@/types/candidate";
import { fetchJobs } from "@/store/slices/jobsSlice";
import { selectJobs } from "@/store/selectors/jobsSelectors";
import { ApiService, type EditCandidateRequest } from "@/services/api";
import { useUser } from "@/contexts/user-context";



interface EditCandidateModalProps {
  candidate: Candidate | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedCandidate: Candidate) => void;
}

interface FileUpload {
  id?: number;
  file?: File;
  filename: string;
  data?: string;
  extension: string;
  isExisting?: boolean;
}

export function EditCandidateModal({
  candidate,
  isOpen,
  onClose,
  onSave,
}: EditCandidateModalProps) {
  const { userId, userRole, userName, userEmail } = useUser();
  const dispatch = useAppDispatch();
  const jobs = useAppSelector(selectJobs);
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState<FileUpload[]>([]);
  const [removeFileIds, setRemoveFileIds] = useState<number[]>([]);
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  // Form state
  const [formData, setFormData] = useState({
    name: "",
    mobile: "",
    email: "",
    client: "",
    current_company: "",
    position: "",
    profile: "",
    current_job_location: "",
    preferred_job_location: "",
    qualifications: "",
    experience: "",
    relevant_experience: "",
    current_ctc: "",
    expected_ctc: "",
    reason_for_job_change: "",
    linkedin: "",
    remarks: "",
    skills: "",
    serving_notice_period: "no",
    period_of_notice: "",
    last_working_date: "",
    buyout: false,
    holding_offer: "no",
    total_offers: "",
    highest_package: "",
    job_id: "",
  });

  useEffect(() => {
    if (jobs.length === 0) {
      const username = userName || userEmail || "managerone";
      dispatch(fetchJobs({ username }));
    }
  }, [dispatch, userName, userEmail, jobs.length]);

  // Create job options for the dropdown
  const jobOptions = jobs.map((job) => ({
    value: job.id.toString(),
    label: `${job.id} - ${job.client} - ${job.role}`,
  }));
  // Initialize form data when candidate changes
  useEffect(() => {
    if (candidate && isOpen) {
      setFormData({
        name: candidate.name,
        mobile: candidate.phone,
        email: candidate.email,
        client: candidate.client,
        current_company: candidate.currentCompany, // These fields might need to be added to your Candidate type
        position: candidate.position,
        profile: candidate.profile,
        current_job_location: candidate.currentJobLocation,
        preferred_job_location: candidate.preferredJobLocation,
        qualifications: candidate.education,
        experience: candidate.experience.toString(),
        relevant_experience: candidate.experience.toString(),
        current_ctc: candidate.salary.split(" - ")[0] || "",
        expected_ctc: candidate.salary.split(" - ")[1] || "",
        reason_for_job_change: candidate.comment,
        linkedin: "", // Add to Candidate type if needed
        remarks: candidate.notes,
        skills: candidate.skills,
        serving_notice_period: "no",
        period_of_notice: "",
        last_working_date: "",
        buyout: false,
        holding_offer: "no",
        total_offers: "",
        highest_package: "",
        job_id: candidate.jobId,
      });
      setFiles([]);
      setRemoveFileIds([]);
      setResumeFile(null);
    }
  }, [candidate, isOpen]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selectedFiles = Array.from(event.target.files || []);

    for (const file of selectedFiles) {
      const base64Data = await convertFileToBase64(file);
      const extension = file.name.split(".").pop() || "";

      const newFile: FileUpload = {
        file,
        filename: file.name,
        data: base64Data,
        extension,
        isExisting: false,
      };

      setFiles((prev) => [...prev, newFile]);
    }
  };

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 data
        const base64Data = result.split(",")[1];
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handleJobChange = (value: string) => {
    handleInputChange("job_id", value);

    // Find the selected job and update related fields
    const selectedJob = jobs.find(job => job.id.toString() === value);
    if (selectedJob) {
      // Update client and profile fields automatically
      handleInputChange("client", selectedJob.client);
      handleInputChange("profile", selectedJob.role);

      // Show success message for user feedback
    }
  };
  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setResumeFile(file);
    }
  };

  const handleRemoveFile = (index: number) => {
    const file = files[index];
    if (file.isExisting && file.id) {
      setRemoveFileIds((prev) => [...prev, file.id!]);
    }
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!candidate || !userId) {
      // Missing candidate or user ID
      toast.error("Missing candidate or user ID");
      return;
    }

    setLoading(true);

    try {
      const requestData: EditCandidateRequest = {
        user_id: userId,
        ...formData,
        buyout: formData.buyout,
        total_offers: formData.total_offers
          ? parseInt(formData.total_offers)
          : undefined,
        highest_package: formData.highest_package
          ? parseFloat(formData.highest_package)
          : undefined,
        pdfs: files
          .filter((file) => !file.isExisting && file.data)
          .map((file) => ({
            filename: file.filename,
            data: file.data!,
            extension: file.extension,
          })),
        remove_file_id: removeFileIds.length > 0 ? removeFileIds : undefined,
      };

      const response = await ApiService.editCandidate(
        candidate.id,
        requestData
      );

      if (response.status === "success") {
        // Create updated candidate object
        const updatedCandidate: Candidate = {
          ...candidate,
          name: formData.name,
          phone: formData.mobile,
          email: formData.email,
          client: formData.client,
          profile: formData.profile,
          position:formData.position,
          location: formData.preferred_job_location,
          education: formData.qualifications,
          experience: parseFloat(formData.experience) || 0,
          salary: `${formData.current_ctc} - ${formData.expected_ctc}`,
          comment: formData.reason_for_job_change,
          notes: formData.remarks,
          skills: formData.skills,
          jobId: formData.job_id,
          lastUpdated: new Date().toISOString().split("T")[0],
        };

        onSave(updatedCandidate);
        onClose();
        toast.success("Candidate updated successfully!");
      } else {
        throw new Error(response.message || "Failed to update candidate");
      }
    } catch (error) {
      // Error updating candidate
      toast.error("Failed to update candidate");
    } finally {
      setLoading(false);
    }
  };

  if (!candidate) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Edit Candidate - {candidate.name}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              {/* <X className="h-4 w-4" /> */}
            </Button>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

            {userRole === "manager" && (
              <div>
                <Label htmlFor="job_id">
                  <span className="text-red-500">*</span>Job ID
                </Label>
                <Select
                  value={formData.job_id}
                  onValueChange={handleJobChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="-- Select Job ID --" />
                  </SelectTrigger>
                  <SelectContent>
                    {jobOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            <div>
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="mobile">Mobile *</Label>
              <Input
                id="mobile"
                value={formData.mobile}
                onChange={(e) => handleInputChange("mobile", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="client">Client *</Label>
              <Input
                id="client"
                value={formData.client}
                onChange={(e) => handleInputChange("client", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="current_company">Current Company</Label>
              <Input
                id="current_company"
                value={formData.current_company}
                onChange={(e) =>
                  handleInputChange("current_company", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="position">Position *</Label>
              <Input
                id="position"
                value={formData.position}
                onChange={(e) => handleInputChange("position", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="profile">Profile *</Label>
              <Input
                id="profile"
                value={formData.profile}
                onChange={(e) => handleInputChange("profile", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="current_job_location">Current Job Location</Label>
              <Input
                id="current_job_location"
                value={formData.current_job_location}
                onChange={(e) =>
                  handleInputChange("current_job_location", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="preferred_job_location">
                Preferred Job Location
              </Label>
              <Input
                id="preferred_job_location"
                value={formData.preferred_job_location}
                onChange={(e) =>
                  handleInputChange("preferred_job_location", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="qualifications">Qualifications</Label>
              <Input
                id="qualifications"
                value={formData.qualifications}
                onChange={(e) =>
                  handleInputChange("qualifications", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="experience">Total Experience (Years)</Label>
              <Input
                id="experience"
                type="number"
                step="0.1"
                value={formData.experience}
                onChange={(e) =>
                  handleInputChange("experience", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="relevant_experience">
                Relevant Experience (Years)
              </Label>
              <Input
                id="relevant_experience"
                type="number"
                step="0.1"
                value={formData.relevant_experience}
                onChange={(e) =>
                  handleInputChange("relevant_experience", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="current_ctc">Current CTC</Label>
              <Input
                id="current_ctc"
                value={formData.current_ctc}
                onChange={(e) =>
                  handleInputChange("current_ctc", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="expected_ctc">Expected CTC</Label>
              <Input
                id="expected_ctc"
                value={formData.expected_ctc}
                onChange={(e) =>
                  handleInputChange("expected_ctc", e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="linkedin">LinkedIn Profile</Label>
              <Input
                id="linkedin"
                value={formData.linkedin}
                onChange={(e) => handleInputChange("linkedin", e.target.value)}
              />
            </div>


          </div>

          {/* Notice Period Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Notice Period Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="serving_notice_period">
                  Serving Notice Period
                </Label>
                <Select
                  value={formData.serving_notice_period}
                  onValueChange={(value) =>
                    handleInputChange("serving_notice_period", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.serving_notice_period === "yes" && (
                <div>
                  <Label htmlFor="last_working_date">Last Working Date</Label>
                  <Input
                    id="last_working_date"
                    type="date"
                    value={formData.last_working_date}
                    onChange={(e) =>
                      handleInputChange("last_working_date", e.target.value)
                    }
                  />
                </div>
              )}

              {formData.serving_notice_period === "no" && (
                <div>
                  <Label htmlFor="period_of_notice">Period of Notice</Label>
                  <Input
                    id="period_of_notice"
                    value={formData.period_of_notice}
                    onChange={(e) =>
                      handleInputChange("period_of_notice", e.target.value)
                    }
                  />
                </div>
              )}

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="buyout"
                  checked={formData.buyout}
                  onChange={(e) =>
                    handleInputChange("buyout", e.target.checked)
                  }
                />
                <Label htmlFor="buyout">Buyout Available</Label>
              </div>
            </div>
          </div>

          {/* Holding Offer Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Offer Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="holding_offer">Holding Offer</Label>
                <Select
                  value={formData.holding_offer}
                  onValueChange={(value) =>
                    handleInputChange("holding_offer", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.holding_offer === "yes" && (
                <>
                  <div>
                    <Label htmlFor="total_offers">Total Offers</Label>
                    <Input
                      id="total_offers"
                      type="number"
                      value={formData.total_offers}
                      onChange={(e) =>
                        handleInputChange("total_offers", e.target.value)
                      }
                    />
                  </div>

                  <div>
                    <Label htmlFor="highest_package">
                      Highest Package (LPA)
                    </Label>
                    <Input
                      id="highest_package"
                      type="number"
                      step="0.1"
                      value={formData.highest_package}
                      onChange={(e) =>
                        handleInputChange("highest_package", e.target.value)
                      }
                    />
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Text Areas */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="skills">Skills</Label>
              <Textarea
                id="skills"
                value={formData.skills}
                onChange={(e) => handleInputChange("skills", e.target.value)}
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="reason_for_job_change">
                Reason for Job Change
              </Label>
              <Textarea
                id="reason_for_job_change"
                value={formData.reason_for_job_change}
                onChange={(e) =>
                  handleInputChange("reason_for_job_change", e.target.value)
                }
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="remarks">Remarks</Label>
              <Textarea
                id="remarks"
                value={formData.remarks}
                onChange={(e) => handleInputChange("remarks", e.target.value)}
                rows={3}
              />
            </div>
          </div>
          {/* Resume Upload Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Resume</h3>
            <div>
              <Label htmlFor="resume">
                <span className="text-red-500">*</span>Resume:{" "}
                {candidate?.resumePresent === true ? (
                  <span style={{ color: "green" }}>previously uploaded</span>
                ) : (
                  <span style={{ color: "red" }}>Not uploaded previously</span>
                )}
              </Label>
              <Input
                id="resume"
                name="resume"
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleResumeUpload}
                className="mt-1"
              />
              {resumeFile && (
                <p className="text-sm text-gray-600 mt-1">
                  Selected: {resumeFile.name}
                </p>
              )}
            </div>
          </div>

          {/* File Upload Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Additional Files</h3>
            <div>
              <Label htmlFor="file-upload">Upload Additional Files</Label>
              <Input
                id="file-upload"
                type="file"
                multiple
                accept=".pdf,.doc,.docx"
                onChange={handleFileUpload}
                className="mt-1"
              />
            </div>

            {files.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Uploaded Files:</h4>
                {files.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <span className="text-sm">{file.filename}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFile(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? "Updating..." : "Update Candidate"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
