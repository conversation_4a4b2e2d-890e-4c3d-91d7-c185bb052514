import React, { createContext, useContext, ReactNode } from 'react';
import { useRealtimeUpdates } from '@/hooks/use-realtime-updates';
import { useUser } from './user-context';

interface RealtimeContextType {
    isConnected: boolean;
    connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
    lastUpdate: Date | null;
    reconnect: () => void;
}

const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined);

interface RealtimeProviderProps {
    children: ReactNode;
}

export const RealtimeProvider: React.FC<RealtimeProviderProps> = ({ children }) => {
    const { isAuthenticated } = useUser();

    const realtimeUpdates = useRealtimeUpdates({
        enableSSE: true,
        enablePolling: true,
        pollingInterval: 120000, // 2 minutes
        showNotifications: true
    });

    // Only provide real-time updates when user is authenticated
    const contextValue = isAuthenticated ? realtimeUpdates : {
        isConnected: false,
        connectionStatus: 'disconnected' as const,
        lastUpdate: null,
        reconnect: () => { }
    };

    return (
        <RealtimeContext.Provider value={contextValue}>
            {children}
        </RealtimeContext.Provider>
    );
};

export const useRealtime = (): RealtimeContextType => {
    const context = useContext(RealtimeContext);
    if (context === undefined) {
        throw new Error('useRealtime must be used within a RealtimeProvider');
    }
    return context;
};
