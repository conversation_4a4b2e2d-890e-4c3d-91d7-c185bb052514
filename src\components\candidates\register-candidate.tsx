import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { useUser } from "@/contexts/user-context";
import { useLocation, useParams, useNavigate } from "react-router-dom";
import { fetchJobs } from "@/store/slices/jobsSlice";
import {
  selectClientCompanies,
  selectJobs,
  selectJobsLoading,
  type ClientCompany,
} from "@/store/selectors/jobsSelectors";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { ApiService } from "@/services/api";
import {
  checkCandidate,
  addCandidate,
  upsertCandidate,
} from "@/store/slices/candidatesSlice";

import { selectApiResponse } from "@/store/selectors/candidatesSelectors";
// Helper function to get status-based styling
const getCompanyStatusStyling = (status: ClientCompany["status"]) => {
  switch (status) {
    case "Closed":
      return {
        border: "border-red-300",
        background: "bg-red-50",
        hover: "hover:bg-red-100",
        text: "text-red-700",
        badge: "bg-red-100 text-red-800",
      };
    case "Hold":
      return {
        border: "border-yellow-300",
        background: "bg-yellow-50",
        hover: "hover:bg-yellow-100",
        text: "text-yellow-700",
        badge: "bg-yellow-100 text-yellow-800",
      };
    case "Active":
    default:
      return {
        border: "border-gray-200",
        background: "bg-white",
        hover: "hover:bg-gray-50",
        text: "text-gray-700",
        badge: "bg-green-100 text-green-800",
      };
  }
};

// Interface for form data
interface CandidateFormData {
  jobId: string;
  name: string;
  mobile: string;
  email: string;
  client: string;
  profile: string;
  skills: string;
  qualifications: string;
  resume: File | null;
  additionalFiles: File[];
  // Compensation
  currentCTC: string;
  currentCTCCurrencyType: "INR" | "USD" | "EUR" | "GBP";
  expectedCTC: string;
  expectedCTCCurrencyType: "INR" | "USD" | "EUR" | "GBP";
  // Other details
  reasonForJobChange: string;
  currentCompany: string;
  currentJobPosition: string;
  currentJobLocation: string;
  preferredJobLocation: string;
  totalExperienceYears: string;
  totalExperienceMonths: string;
  relevantExperienceYears: string;
  relevantExperienceMonths: string;
  noticePeriod: string; // Added for Serving Notice Period
  joiningOffer: string; // Added for Holding Offer
  linkedinUrl: string; // Added for LinkedIn URL
  remarks: string; // Added for Remarks
  // Additional fields for notice period and joining offer
  lastWorkingDate: string;
  noticePeriodBuyout: boolean;
  joiningOfferBuyout: boolean;
  noticePeriodDays: string;
}

// Helper function to convert file to base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === "string") {
        // Remove the data:application/pdf;base64, prefix
        const base64String = reader.result.split(",")[1];
        resolve(base64String);
      } else {
        reject(new Error("Failed to convert file to base64"));
      }
    };
    reader.onerror = (error) => reject(error);
  });
};

export function RegisterCandidate() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { userName, userEmail } = useUser();
  const location = useLocation();
  const params = useParams();

  // Get job details or candidate from navigation state or URL params
  const jobDetails = location.state as {
    jobId?: string;
    client?: string;
    role?: string;
    jobStatus?: string;
    candidate?: any;
    isTalentPoolMode?: boolean;
  } | null;

  // Check if we're in talent pool mode
  const isTalentPoolMode = jobDetails?.isTalentPoolMode || false;

  // If editing, get candidate object
  const apiResponse = useAppSelector(selectApiResponse);

  const candidateToEdit = jobDetails?.candidate;
  const editMode = !!candidateToEdit;

  // Redux selectors
  const clientCompanies = useAppSelector(selectClientCompanies);
  const jobs = useAppSelector(selectJobs);
  const jobsLoading = useAppSelector(selectJobsLoading);

  // State for the selected company
  const [selectedCompany, setSelectedCompany] = useState<string | null>(
    isTalentPoolMode ? "talent-pool" : (
      jobDetails?.client ||
      (candidateToEdit ? candidateToEdit.client : null) ||
      null
    )
  );

  // State for search and pagination
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const companiesPerPage = 20; // List layout - show more companies per page

  // State for resume parsing
  const [isResumeParsing, setIsResumeParsing] = useState(false);

  // State for candidate submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [duplicateData, setDuplicateData] = useState<any>(null);
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);

  // Animation hook for pagination
  const { animatePagination } = useTableAnimation();

  // State for form data
  const [formData, setFormData] = useState<CandidateFormData>({
    jobId: isTalentPoolMode ? "" : (candidateToEdit?.jobId || jobDetails?.jobId || params.jobId || ""),
    // Ensure full name in edit mode
    name: candidateToEdit
      ? `${candidateToEdit.firstName || ""} ${candidateToEdit.lastName || ""
        }`.trim() ||
      candidateToEdit.name ||
      ""
      : "",
    mobile: candidateToEdit?.mobile || candidateToEdit?.phone || "",
    email: candidateToEdit?.email || "",
    client: isTalentPoolMode ? "Talent Pool" : (candidateToEdit?.client || jobDetails?.client || ""),
    profile: isTalentPoolMode ? "Talent Pool" : (candidateToEdit?.profile || jobDetails?.role || ""),
    skills: candidateToEdit?.skills || "",
    qualifications: candidateToEdit?.qualifications || "",
    resume: null, // File fields cannot be pre-filled
    additionalFiles: [], // File fields cannot be pre-filled
    // Compensation defaults
    currentCTC: candidateToEdit?.currentCTC || candidateToEdit?.salary || "",
    currentCTCCurrencyType:
      (candidateToEdit?.currentCTCCurrencyType as any) || "INR",
    expectedCTC: candidateToEdit?.expectedCTC || "",
    expectedCTCCurrencyType:
      (candidateToEdit?.expectedCTCCurrencyType as any) || "INR",
    // Other details
    reasonForJobChange: candidateToEdit?.reasonForJobChange || "",
    currentCompany: candidateToEdit?.currentCompany || "",
    currentJobPosition: candidateToEdit?.currentJobPosition || "",
    currentJobLocation: candidateToEdit?.currentJobLocation || "",
    preferredJobLocation: candidateToEdit?.preferredJobLocation || "",
    totalExperienceYears: candidateToEdit?.totalExperienceYears || "0",
    totalExperienceMonths: candidateToEdit?.totalExperienceMonths || "0",
    relevantExperienceYears: candidateToEdit?.relevantExperienceYears || "0",
    relevantExperienceMonths: candidateToEdit?.relevantExperienceMonths || "0",
    noticePeriod: candidateToEdit?.noticePeriod || "",
    joiningOffer: candidateToEdit?.joiningOffer || "",
    linkedinUrl: candidateToEdit?.linkedinUrl || "",
    remarks: candidateToEdit?.remarks || "",
    // Additional fields for notice period and joining offer
    lastWorkingDate: candidateToEdit?.lastWorkingDate || "",
    noticePeriodBuyout: candidateToEdit?.noticePeriodBuyout || false,
    joiningOfferBuyout: candidateToEdit?.joiningOfferBuyout || false,
    noticePeriodDays: candidateToEdit?.noticePeriodDays || "",

  });

  // Fetch jobs data on component mount
  useEffect(() => {
    if (jobs.length === 0) {
      dispatch(fetchJobs({ username: userName || userEmail || "managerone" }));
    }
  }, [dispatch, userName, userEmail, jobs.length]);

  // Update form data when job details or candidateToEdit are available
  useEffect(() => {
    if (candidateToEdit) {
      setFormData((prev) => ({
        ...prev,
        ...candidateToEdit,
        name:
          `${candidateToEdit.firstName || ""} ${candidateToEdit.lastName || ""
            }`.trim() ||
          candidateToEdit.name ||
          prev.name,
        mobile: candidateToEdit.mobile || candidateToEdit.phone || prev.mobile,
        // keep files empty
        resume: null,
        additionalFiles: [],
        // ensure compensation fields are present (fallbacks)
        currentCTC:
          (candidateToEdit as any).currentCTC ||
          (candidateToEdit as any).salary ||
          prev.currentCTC ||
          "",
        expectedCTC:
          (candidateToEdit as any).expectedCTC || prev.expectedCTC || "",
        currentCTCCurrencyType:
          ((candidateToEdit as any).currentCTCCurrencyType as any) ||
          prev.currentCTCCurrencyType ||
          "INR",
        expectedCTCCurrencyType:
          ((candidateToEdit as any).expectedCTCCurrencyType as any) ||
          prev.expectedCTCCurrencyType ||
          "INR",
      }));
      if (candidateToEdit.client) {
        setSelectedCompany(candidateToEdit.client);
      }
    } else if (jobDetails && !isTalentPoolMode) {
      setFormData((prev) => ({
        ...prev,
        jobId: jobDetails.jobId || prev.jobId,
        client: jobDetails.client || prev.client,
        profile: jobDetails.role || prev.profile,
      }));
      if (jobDetails.client) {
        setSelectedCompany(jobDetails.client);
      }
    }
  }, [candidateToEdit, jobDetails]);

  // Prefill from API response if the candidate matches id
  useEffect(() => {
    if (candidateToEdit && apiResponse?.candidates?.length) {
      const apiCandidate = apiResponse.candidates.find(
        (c: any) => c.id === candidateToEdit.id
      );
      if (apiCandidate) {
        setFormData((prev) => ({
          ...prev,
          name: apiCandidate.name || prev.name,
          email: apiCandidate.email || prev.email,
          mobile: apiCandidate.mobile || prev.mobile,
          client: apiCandidate.client || prev.client,
          profile: apiCandidate.profile || prev.profile,
          skills: apiCandidate.skills || prev.skills,
          qualifications: apiCandidate.qualifications || prev.qualifications,
          currentCompany: apiCandidate.current_company || prev.currentCompany,
          currentJobLocation:
            apiCandidate.current_job_location || prev.currentJobLocation,
          preferredJobLocation:
            apiCandidate.preferred_job_location || prev.preferredJobLocation,
          reasonForJobChange:
            apiCandidate.reason_for_job_change || prev.reasonForJobChange,
          totalExperienceYears: apiCandidate.experience
            ? Math.floor(parseFloat(apiCandidate.experience)).toString()
            : prev.totalExperienceYears,
          totalExperienceMonths: apiCandidate.experience
            ? Math.round(
              (parseFloat(apiCandidate.experience) % 1) * 12
            ).toString()
            : prev.totalExperienceMonths,
          relevantExperienceYears: apiCandidate.relevant_experience
            ? Math.floor(
              parseFloat(apiCandidate.relevant_experience)
            ).toString()
            : prev.relevantExperienceYears,
          relevantExperienceMonths: apiCandidate.relevant_experience
            ? Math.round(
              (parseFloat(apiCandidate.relevant_experience) % 1) * 12
            ).toString()
            : prev.relevantExperienceMonths,
          noticePeriod: apiCandidate.serving_notice_period || prev.noticePeriod,
          joiningOffer: apiCandidate.holding_offer || prev.joiningOffer,
          linkedinUrl: apiCandidate.linkedin || prev.linkedinUrl,
          remarks: apiCandidate.remarks || prev.remarks,
          // Parse currency and amounts like "USD 110000"
          currentCTC: (apiCandidate.current_ctc || "").replace(
            /^[A-Z]{3}\s*/i,
            ""
          ),
          expectedCTC: (apiCandidate.expected_ctc || "").replace(
            /^[A-Z]{3}\s*/i,
            ""
          ),
          currentCTCCurrencyType:
            (apiCandidate.current_ctc?.slice(0, 3)?.toUpperCase() as any) ||
            prev.currentCTCCurrencyType,
          expectedCTCCurrencyType:
            (apiCandidate.expected_ctc?.slice(0, 3)?.toUpperCase() as any) ||
            prev.expectedCTCCurrencyType,
        }));
      }
    }
  }, [candidateToEdit, apiResponse]);

  // Filter companies based on search term
  const filteredCompanies = clientCompanies.filter((company) =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Paginate filtered companies
  const totalPages = Math.ceil(filteredCompanies.length / companiesPerPage);
  const startIndex = (currentPage - 1) * companiesPerPage;
  const paginatedCompanies = filteredCompanies.slice(
    startIndex,
    startIndex + companiesPerPage
  );

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Enhanced job filtering with better error handling
  const getAvailableJobsForClient = (clientName: string) => {
    if (!clientName || !jobs.length) return [];

    const clientJobs = jobs.filter((job) => {
      const jobClient = job.client?.trim().toLowerCase();
      const targetClient = clientName.toLowerCase();

      return jobClient === targetClient && job.job_status === "Active";
    });

    // Sort by job ID (newest first) and then by role
    return clientJobs.sort((a, b) => {
      // First sort by job ID (descending - newest first)
      if (a.id !== b.id) {
        return b.id - a.id;
      }
      // Then sort by role alphabetically
      return (a.role || "").localeCompare(b.role || "");
    });
  };

  // Get available jobs for the selected company
  const availableJobs = selectedCompany
    ? getAvailableJobsForClient(selectedCompany)
    : [];

  // Handle company selection
  const handleCompanySelect = (companyId: string) => {
    setSelectedCompany(companyId);
    const company = clientCompanies.find((c) => c.id === companyId);
    if (company) {
      setFormData({
        ...formData,
        client: company.name,
        jobId: "", // Reset job ID when company changes
      });

      // Show warning if company has no active jobs
      if (company.jobCounts.active === 0) {
        toast.warning(
          `No active jobs available for ${company.name}. You can still proceed but may need to contact the client.`
        );
      }
    }
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Auto-populate profile when job is selected
    if (name === "jobId" && value) {
      const selectedJob = availableJobs.find(
        (job) => job.id.toString() === value
      );
      if (selectedJob) {
        setFormData((prev) => ({
          ...prev,
          profile: selectedJob.role || prev.profile,
          // Also update client if not already set
          client: selectedJob.client || prev.client,
        }));
      }
    }
  };

  // Handle file upload
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Set the file first
      setFormData({
        ...formData,
        resume: file,
      });

      // Set loading state
      setIsResumeParsing(true);

      try {
        // Convert file to base64
        const base64String = await fileToBase64(file);

        // Parse resume using API
        const resumeData = await ApiService.parseResume(base64String);

        // Check if we got valid data
        const {
          name,
          mail,
          skill1,
          phone,
          current_company,
          position,
          current_job_location,
          qualifications,
        } = resumeData;

        if (
          name ||
          mail ||
          skill1 ||
          phone ||
          current_company ||
          position ||
          current_job_location ||
          qualifications
        ) {
          // Map the parsed data to form fields
          setFormData((prev) => ({
            ...prev,
            name: name || prev.name,
            email: mail || prev.email,
            skills: skill1 || prev.skills,
            mobile: phone || prev.mobile,
            currentCompany: current_company || prev.currentCompany,
            currentJobPosition: position || prev.currentJobPosition,
            currentJobLocation: current_job_location || prev.currentJobLocation,
            qualifications: qualifications || prev.qualifications,
          }));

          toast.success(
            "Resume parsed successfully! Form fields have been updated."
          );
        } else {
          toast.warning(
            "Resume parsed but no relevant data found. Please fill in the details manually."
          );
        }
      } catch (error) {
        console.error("Resume parsing failed:", error);
        toast.error(
          "Failed to parse resume. Please fill in the details manually."
        );
      } finally {
        setIsResumeParsing(false);
      }
    }
  };

  // Handle additional files upload
  const handleAdditionalFilesChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setFormData({
        ...formData,
        additionalFiles: [...formData.additionalFiles, ...newFiles],
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate total experience vs relevant experience



      // if (relevantExpInMonths > totalExpInMonths) {
      //   toast.error(
      //     "Relevant experience cannot be greater than total experience!"
      //   );
      //   return;
      // }

      // // Validate conditional fields
      // if (formData.noticePeriod === "Yes" && !formData.lastWorkingDate) {
      //   toast.error(
      //     "Last working date is required when serving notice period is 'Yes'!"
      //   );
      //   return;
      // }

      // if (formData.noticePeriod === "No" && !formData.noticePeriodDays) {
      //   toast.error(
      //     "Notice period days are required when serving notice period is 'No'!"
      //   );
      //   return;
      // }

      // if (formData.noticePeriod === "No" && !formData.noticePeriodDays.trim()) {
      //   toast.error("Notice period days must be specified when not serving notice period!");
      //   return;
      // }

      // if (formData.joiningOffer === "Yes" && !formData.lastWorkingDate) {
      //   toast.error(
      //     "Last working date is required when holding offer is 'Yes'!"
      //   );
      //   return;
      // }

      // if (formData.joiningOffer === "Yes" && !formData.lastWorkingDate.trim()) {
      //   toast.error("Last working date must be specified when holding an offer!");
      //   return;
      // }

      // if (formData.joiningOffer === "No" && !formData.noticePeriodDays) {
      //   toast.error(
      //     "Notice period days are required when holding offer is 'No'!"
      //   );
      //   return;
      // }

      // // Check if resume is uploaded
      // if (!formData.resume) {
      //   toast.error("Resume is required!");
      //   return;
      // }

      // // Validate basic required fields for all modes
      // if (!formData.name.trim()) {
      //   toast.error("Full Name is required!");
      //   return;
      // }
      // if (!formData.mobile.trim()) {
      //   toast.error("Mobile number is required!");
      //   return;
      // }
      // if (!formData.email.trim()) {
      //   toast.error("Email is required!");
      //   return;
      // }
      // if (!formData.skills.trim()) {
      //   toast.error("Skills are required!");
      //   return;
      // }
      // if (!formData.qualifications.trim()) {
      //   toast.error("Qualifications are required!");
      //   return;
      // }
      // if (!formData.totalExperienceYears || !formData.totalExperienceMonths) {
      //   toast.error("Total experience (years and months) is required!");
      //   return;
      // }
      // if (!formData.relevantExperienceYears || !formData.relevantExperienceMonths) {
      //   toast.error("Relevant experience (years and months) is required!");
      //   return;
      // }

      // // Validate experience values are valid numbers
      // if (isNaN(parseInt(formData.totalExperienceYears)) || isNaN(parseInt(formData.totalExperienceMonths))) {
      //   toast.error("Total experience must be valid numbers!");
      //   return;
      // }
      // if (isNaN(parseInt(formData.relevantExperienceYears)) || isNaN(parseInt(formData.relevantExperienceMonths))) {
      //   toast.error("Relevant experience must be valid numbers!");
      //   return;
      // }
      // if (!formData.currentCTC.trim()) {
      //   toast.error("Current CTC is required!");
      //   return;
      // }
      // if (!formData.expectedCTC.trim()) {
      //   toast.error("Expected CTC is required!");
      //   return;
      // }

      // // Validate CTC values are valid numbers
      // if (isNaN(parseFloat(formData.currentCTC))) {
      //   toast.error("Current CTC must be a valid number!");
      //   return;
      // }
      // if (isNaN(parseFloat(formData.expectedCTC))) {
      //   toast.error("Expected CTC must be a valid number!");
      //   return;
      // }
      // if (!formData.noticePeriod) {
      //   toast.error("Notice Period is required!");
      //   return;
      // }
      // if (!formData.joiningOffer) {
      //   toast.error("Holding Offer status is required!");
      //   return;
      // }

      // // Validate job-related fields only when not in talent pool mode
      // if (!isTalentPoolMode) {
      //   if (!formData.client || !formData.profile) {
      //     toast.error("Client and Profile are required!");
      //     return;
      //   }
      //   if (!formData.jobId) {
      //     toast.error("Job ID is required!");
      //     return;
      //   }
      // }

      // // Check if last working date is not in past
      // if (formData.lastWorkingDate) {
      //   const currentDate = new Date().setHours(0, 0, 0, 0);
      //   const selectedDate = new Date(formData.lastWorkingDate).setHours(
      //     0,
      //     0,
      //     0,
      //     0
      //   );

      //   if (selectedDate < currentDate) {
      //     toast.error("Last working date cannot be in the past!");
      //     return;
      //   }
      // }

      // Check for duplicate candidate
      const checkData = {
        name: formData.name.trim(),
        mobile: formData.mobile.trim(),
        email: formData.email.trim(),
        total_experience_years: formData.totalExperienceYears,
        total_experience_months: formData.totalExperienceMonths,
        relevant_experience_years: formData.relevantExperienceYears,
        relevant_experience_months: formData.relevantExperienceMonths,
        qualifications: formData.qualifications.trim(),
        last_working_date: formData.lastWorkingDate || "",
        serving_notice_period: formData.noticePeriod,
      };

      const duplicateCheck = await dispatch(checkCandidate(checkData)).unwrap();

      if (duplicateCheck.jobIds && duplicateCheck.jobIds.length > 0) {
        // Duplicate found - show warning
        setDuplicateData(duplicateCheck);
        setShowDuplicateWarning(true);
        return;
      }
      await registerCandidate();
    } catch (error) {
      toast.error("Failed to submit form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const registerCandidate = async () => {
    try {
      const resumeBase64 = await fileToBase64(formData.resume!);
      let pdfs: Array<{ data: string; extension: string; filename: string }> =
        [];
      if (formData.additionalFiles.length > 0) {
        pdfs = await Promise.all(
          formData.additionalFiles.map(async (file) => ({
            data: await fileToBase64(file),
            extension: file.name.split(".").pop() || "",
            filename: file.name,
          }))
        );
      }

      const candidateData = {
        job_id: isTalentPoolMode ? 0 : (parseInt(formData.jobId) || 0),
        user_id: localStorage.getItem("userId") || "",
        name: formData.name.trim(),
        mobile: formData.mobile.trim(),
        email: formData.email.trim(),
        client: isTalentPoolMode ? "Talent Pool" : formData.client,
        profile: isTalentPoolMode ? "Talent Pool" : formData.profile,
        skills: formData.skills,
        qualifications: formData.qualifications,
        current_ctc: `${formData.currentCTCCurrencyType} ${formData.currentCTC}`,
        expected_ctc: `${formData.expectedCTCCurrencyType} ${formData.expectedCTC}`,
        experience: `${formData.totalExperienceYears}.${formData.totalExperienceMonths}`,
        relevant_experience: `${formData.relevantExperienceYears}.${formData.relevantExperienceMonths}`,
        reason_for_job_change: formData.reasonForJobChange,
        current_company: formData.currentCompany,
        current_job_position: formData.currentJobPosition,
        current_job_location: formData.currentJobLocation,
        preferred_job_location: formData.preferredJobLocation,
        notice_period: formData.noticePeriodDays,
        joining_offer: formData.joiningOffer,
        linkedin_url: formData.linkedinUrl,
        remarks: formData.remarks,
        serving_notice_period: formData.noticePeriod,
        period_of_notice: formData.noticePeriodDays,
        last_working_date: formData.lastWorkingDate,
        buyout: formData.noticePeriodBuyout || formData.joiningOfferBuyout,
        holding_offer: formData.joiningOffer,
        resume: resumeBase64,
        pdfs: pdfs.length > 0 ? pdfs : undefined,
        client_assigned: isTalentPoolMode ? false : true,
      };
      console.log("jsnkvnlk lkl", candidateData)


      // Add candidate via API
      const response = await dispatch(addCandidate(candidateData)).unwrap();

      if (response.status !== "error" && response.candidate_id) {
        // Success - update Redux state
        const newCandidate = {
          id: response.candidate_id,
          jobId: formData.jobId,
          name: formData.name,
          email: formData.email,
          phone: formData.mobile,
          client: formData.client,
          profile: formData.profile,
          skills: formData.skills,
          status: "Internal Screening",
          appliedDate:
            response.date_created || new Date().toISOString().split("T")[0],
          source: "Manual Registration",
          experience: parseFloat(
            `${formData.totalExperienceYears}.${formData.totalExperienceMonths}`
          ),
          education: formData.qualifications,
          location: formData.preferredJobLocation,
          salary: `${formData.currentCTC} - ${formData.expectedCTC}`,
          notes: formData.remarks,
          lastUpdated: new Date().toISOString().split("T")[0],
          comment: formData.reasonForJobChange,
          peerReviewer: "",
          recruiter: userName || userEmail || "",
          management: "",
          resume_present: true,
          clientAssigned: isTalentPoolMode ? false : true,
          // Additional required properties
          currentCompany: formData.currentCompany || "",
          position: formData.currentJobPosition || "",
          currentJobLocation: formData.currentJobLocation || "",
          preferredJobLocation: formData.preferredJobLocation || "",
          relevantExperience: parseFloat(
            `${formData.relevantExperienceYears}.${formData.relevantExperienceMonths}`
          ),
          currentCTC: formData.currentCTC || "",
          expectedCTC: formData.expectedCTC || "",
          noticePeriod: formData.noticePeriod || "",
          linkedin: formData.linkedinUrl || "",
          reasonForJobChange: formData.reasonForJobChange || "",
          holdingOffer: formData.joiningOffer || "",
          servingNoticePeriod: formData.noticePeriod || "",
          periodOfNotice: formData.noticePeriodDays || "",
          lastWorkingDate: formData.lastWorkingDate || null,
          totalOffers: null,
          highestPackageInLpa: null,
          buyout: formData.noticePeriodBuyout || formData.joiningOfferBuyout || false,
          resumePresent: !!formData.resume,
          userId: 0, // Will be set by the API
          comments: {},
        };

        // Add to Redux store
        dispatch(upsertCandidate(newCandidate));

        // Success message and navigation
        if (isTalentPoolMode) {
          toast.success("Candidate added successfully!");
          navigate("/manager/talent-pool");
        } else {
          toast.success("Candidate registered successfully!");
          navigate("/dashboard");
        }
      } else {
        toast.error(response.message || "Failed to register candidate");
      }
    } catch (error) {
      console.error("Candidate registration error:", error);
      toast.error("Failed to register candidate. Please try again.");
    }
  };

  // Handle duplicate warning confirmation
  const handleDuplicateWarningConfirm = async () => {
    setShowDuplicateWarning(false);
    await registerCandidate();
  };

  // Handle duplicate warning cancel
  const handleDuplicateWarningCancel = () => {
    setShowDuplicateWarning(false);
    setDuplicateData(null);
  };

  // Generate options for years (0-30)
  const yearOptions = Array.from({ length: 31 }, (_, i) => (
    <option key={`year-${i}`} value={i.toString()}>
      {i} {i === 1 ? "Year" : "Years"}
    </option>
  ));

  // Generate options for months (0-11)
  const monthOptions = Array.from({ length: 12 }, (_, i) => (
    <option key={`month-${i}`} value={i.toString()}>
      {i} {i === 1 ? "Month" : "Months"}
    </option>
  ));

  // Helper function to check experience validation
  const getExperienceValidationMessage = () => {
    const totalExpYears = parseInt(formData.totalExperienceYears) || 0;
    const totalExpMonths = parseInt(formData.totalExperienceMonths) || 0;
    const relevantExpYears = parseInt(formData.relevantExperienceYears) || 0;
    const relevantExpMonths = parseInt(formData.relevantExperienceMonths) || 0;

    const totalExpInMonths = totalExpYears * 12 + totalExpMonths;
    const relevantExpInMonths = relevantExpYears * 12 + relevantExpMonths;

    if (relevantExpInMonths > totalExpInMonths) {
      return "Relevant experience cannot be greater than total experience";
    }
    return null;
  };

  // Options for notice period and joining offer
  const noticePeriodOptions = [
    "0-15 Days",
    "15-30 Days",
    "30-45 Days",
    "45-60 Days",
    "60-90 Days",
    "90+ Days",
  ];

  const joiningOfferOptions = ["Yes", "No", "Pipeline"];

  return (
    <div className="flex flex-col w-full h-full">
      <div className="bg-white p-4 flex-1 w-full overflow-auto flex flex-col">
        <div className="flex-1 overflow-auto">
          {!editMode && (
            <h2 className="text-2xl font-semibold text-gray-800 mb-2">
              {editMode ? "Edit Candidate" : (isTalentPoolMode ? "Add New Candidate" : "Register Candidate")}
            </h2>
          )}

          {/* Talent Pool Mode Banner with Back Button */}
          {isTalentPoolMode && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="text-sm text-blue-800">
                    <p className="font-medium">Available Candidates Mode</p>
                    <p className="text-blue-700">Candidates added here will be stored for future opportunities without immediate job assignments.</p>
                  </div>
                </div>
                <button
                  onClick={() => navigate("/manager/talent-pool")}
                  className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span>Back to Available Candidates</span>
                </button>
              </div>
            </div>
          )}

          {!isTalentPoolMode && !selectedCompany ? (
            <div className="bg-white p-2 rounded-lg shadow-sm border border-gray-200 w-full">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-medium text-gray-700">
                  Select Client Company
                </h3>
                <div className="flex items-center gap-3">
                  <div className="text-sm text-gray-500">
                    {filteredCompanies.length} companies found
                  </div>
                </div>
              </div>

              {/* Search Bar */}
              <div className="mb-2">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search companies by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {jobsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex flex-col items-center space-y-3">
                    <svg
                      className="animate-spin h-8 w-8 text-blue-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <div className="text-gray-500">
                      Loading companies and jobs...
                    </div>
                  </div>
                </div>
              ) : filteredCompanies.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">
                    {searchTerm
                      ? `No companies found matching "${searchTerm}"`
                      : "No companies found"}
                  </div>
                </div>
              ) : (
                <div className="space-y-2 min-h-[280px] max-h-[380px] overflow-y-auto">
                  {paginatedCompanies.map((company) => {
                    const styling = getCompanyStatusStyling(company.status);
                    return (
                      <div
                        key={company.id}
                        className={`border ${styling.border} ${styling.background
                          } rounded-lg p-2 flex items-center justify-between ${styling.hover
                          } cursor-pointer transition-colors relative ${company.jobCounts.active === 0 ? "opacity-60" : ""
                          }`}
                        onClick={() => handleCompanySelect(company.id)}
                      >
                        {/* Left side - Company info */}
                        <div className="flex items-center space-x-4">
                          {/* Company Avatar */}
                          <div
                            className={`w-10 h-10 ${styling.background} border ${styling.border} rounded-full flex items-center justify-center flex-shrink-0`}
                          >
                            <span
                              className={`text-lg font-bold ${styling.text}`}
                            >
                              {company.name.charAt(0)}
                            </span>
                          </div>

                          {/* Company details */}
                          <div className="flex flex-col">
                            <span
                              className={`text-base font-medium ${styling.text}`}
                            >
                              {company.name}
                            </span>
                            <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                              <span>
                                {company.jobCounts.total} job
                                {company.jobCounts.total !== 1 ? "s" : ""}
                              </span>
                              {company.jobCounts.active > 0 ? (
                                <span className="text-green-600 font-medium">
                                  {company.jobCounts.active} active
                                </span>
                              ) : (
                                <span className="text-amber-600 font-medium">
                                  No active jobs
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Right side - Status and warning */}
                        <div className="flex items-center space-x-3">
                          {/* Warning for companies with no active jobs */}
                          {company.jobCounts.active === 0 && (
                            <div className="flex items-center space-x-1 text-amber-600">
                              <svg
                                className="w-4 h-4"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              <span className="text-xs">No active jobs</span>
                            </div>
                          )}

                          {/* Status Badge */}
                          <div
                            className={`px-3 py-1 text-sm font-medium rounded-full ${styling.badge}`}
                          >
                            {company.status}
                          </div>

                          {/* Arrow indicator */}
                          <svg
                            className="w-5 h-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-700">
                    {filteredCompanies.length > 0 ? (
                      <>
                        Showing {startIndex + 1} to{" "}
                        {Math.min(
                          startIndex + companiesPerPage,
                          filteredCompanies.length
                        )}{" "}
                        of {filteredCompanies.length} companies
                      </>
                    ) : (
                      "No companies found"
                    )}
                  </div>
                  <AnimatedPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={async (page) => {
                      await animatePagination();
                      setCurrentPage(page);
                    }}
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 w-full">
              {!isTalentPoolMode && (
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-medium text-gray-700">
                    {clientCompanies.find((c) => c.id === selectedCompany)
                      ?.name || formData.client}
                  </h3>

                  {!editMode && !jobDetails?.client && (
                    <button
                      onClick={() => setSelectedCompany(null)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      Change Company
                    </button>
                  )}
                </div>
              )}

              {isTalentPoolMode && (
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-medium text-gray-700">
                    Candidate Information
                  </h3>
                </div>
              )}

              <div className="space-y-6 overflow-y-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-2 gap-y-2">
                  {/* Job ID - Hidden in Talent Pool Mode */}
                  {!isTalentPoolMode && (
                    <div className="space-y-1">
                      <label
                        htmlFor="jobId"
                        className="block text-sm font-medium text-gray-700"
                      >
                        * Job ID
                      </label>
                      {availableJobs.length > 0 ? (
                        <select
                          id="jobId"
                          name="jobId"
                          required
                          className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                          value={formData.jobId}
                          onChange={handleInputChange}
                        >
                          <option value="">Select Job ID</option>
                          {availableJobs.map((job) => (
                            <option key={job.id} value={job.id}>
                              {job.id} - {job.role} ({job.location || "Remote"})
                            </option>
                          ))}
                        </select>
                      ) : (
                        <div className="mt-1 space-y-2">
                          <div className="text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-md px-2 py-1.5">
                            <div className="flex items-center">
                              <svg
                                className="w-4 h-4 mr-2"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58-9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              No active jobs available for this client
                            </div>
                            <div className="text-xs text-amber-700 mt-1">
                              You can manually enter a job ID below or contact the
                              client.
                            </div>
                          </div>
                          <input
                            type="text"
                            id="jobId"
                            name="jobId"
                            placeholder="Enter Job ID manually"
                            className="block w-full px-2 py-1.5 border border-amber-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500 text-xs bg-amber-50"
                            value={formData.jobId}
                            onChange={handleInputChange}
                          />
                        </div>
                      )}
                      {/* {availableJobs.length > 0 && (
                      <p className="mt-1 text-xs text-gray-500">
                        {availableJobs.length} active job{availableJobs.length !== 1 ? 's' : ''} available
                      </p>
                    )} */}
                    </div>

                  )}

                  {/* No Jobs Available Message */}
                  {!isTalentPoolMode && selectedCompany && availableJobs.length === 0 && (
                    <div className="col-span-2 space-y-1">
                      <label className="block text-sm font-medium text-gray-700">
                        Job Availability
                      </label>
                      <div className="mt-1 p-3 bg-amber-50 border border-amber-200 rounded-md">
                        <div className="flex items-start space-x-3">
                          <svg
                            className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-amber-800">
                              No Active Jobs Available
                            </h4>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Name */}
                  <div className="space-y-1">
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.name}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Mobile */}
                  <div className="space-y-1">
                    <label
                      htmlFor="mobile"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Mobile
                    </label>
                    <input
                      type="tel"
                      id="mobile"
                      name="mobile"
                      required
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.mobile}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Email */}
                  <div className="space-y-1">
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Client - Hidden in Talent Pool Mode */}
                  {!isTalentPoolMode && (
                    <div className="space-y-1">
                      <label
                        htmlFor="client"
                        className="block text-sm font-medium text-gray-700"
                      >
                        * Client
                      </label>
                      <input
                        type="text"
                        id="client"
                        name="client"
                        required
                        readOnly
                        className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-xs"
                        value={formData.client}
                      />
                    </div>
                  )}

                  {/* Profile - Hidden in Talent Pool Mode */}
                  {!isTalentPoolMode && (
                    <div className="space-y-1">
                      <label
                        htmlFor="profile"
                        className="block text-sm font-medium text-gray-700"
                      >
                        * Profile
                      </label>
                      <input
                        type="text"
                        id="profile"
                        name="profile"
                        required
                        className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                        value={formData.profile}
                      />
                    </div>
                  )}

                  {/* Skills */}
                  <div className="space-y-1">
                    <label
                      htmlFor="skills"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Skills
                    </label>
                    <input
                      type="text"
                      id="skills"
                      name="skills"
                      required
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.skills}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Qualifications */}
                  <div className="space-y-1">
                    <label
                      htmlFor="qualifications"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Qualifications
                    </label>
                    <input
                      type="text"
                      id="qualifications"
                      name="qualifications"
                      required
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.qualifications}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current CTC */}
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Current CTC
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      <select
                        id="currentCTCCurrencyType"
                        name="currentCTCCurrencyType"
                        className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                        value={formData.currentCTCCurrencyType}
                        onChange={handleInputChange}
                      >
                        <option value="INR">INR</option>
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                      </select>
                      <input
                        type="number"
                        id="currentCTC"
                        name="currentCTC"
                        placeholder="Amount"
                        className="col-span-2 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                        value={formData.currentCTC}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  {/* Expected CTC */}
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Expected CTC
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      <select
                        id="expectedCTCCurrencyType"
                        name="expectedCTCCurrencyType"
                        className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                        value={formData.expectedCTCCurrencyType}
                        onChange={handleInputChange}
                      >
                        <option value="INR">INR</option>
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                      </select>
                      <input
                        type="number"
                        id="expectedCTC"
                        name="expectedCTC"
                        placeholder="Amount"
                        className="col-span-2 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                        value={formData.expectedCTC}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  {/* Resume */}
                  <div className="space-y-1 ">
                    <label
                      htmlFor="resume"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Resume
                    </label>
                    <div className="mt-1 flex items-center">
                      <label
                        htmlFor="resume-upload"
                        className={`cursor-pointer py-2 px-3 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${isResumeParsing
                          ? "bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed"
                          : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
                          }`}
                      >
                        {isResumeParsing ? (
                          <div className="flex items-center">
                            <svg
                              className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            Parsing...
                          </div>
                        ) : (
                          "Upload a file"
                        )}
                        <input
                          id="resume-upload"
                          name="resume-upload"
                          type="file"
                          className="sr-only"
                          accept=".pdf,.doc,.docx"
                          onChange={handleFileChange}
                          disabled={isResumeParsing}
                        />
                      </label>
                      <span className="ml-3 text-sm text-gray-500">
                        {formData.resume
                          ? formData.resume.name
                          : "No file chosen"}
                      </span>
                      {formData.resume && !isResumeParsing && (
                        <button
                          type="button"
                          className="ml-2 text-red-600 hover:text-red-800"
                          onClick={() =>
                            setFormData({ ...formData, resume: null })
                          }
                        >
                          Clear
                        </button>
                      )}
                    </div>
                    {isResumeParsing && (
                      <p className="mt-1 text-xs text-blue-600">
                        ⏳ Parsing resume and extracting information...
                      </p>
                    )}
                  </div>

                  {/* Additional Files */}
                  <div className="space-y-1 ">
                    <label
                      htmlFor="additionalFiles"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Additional Files
                    </label>
                    <div className="mt-1 space-y-2">
                      <label
                        htmlFor="additional-files-upload"
                        className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 inline-block"
                      >
                        Upload Files
                        <input
                          id="additional-files-upload"
                          name="additional-files-upload"
                          type="file"
                          multiple
                          className="sr-only"
                          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                          onChange={handleAdditionalFilesChange}
                        />
                      </label>

                      {/* Display selected files */}
                      {formData.additionalFiles.length > 0 && (
                        <div className="space-y-2">
                          <div className="text-sm text-gray-600">
                            {formData.additionalFiles.length} file(s) selected:
                          </div>
                          {formData.additionalFiles.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between bg-gray-50 p-2 rounded-md"
                            >
                              <span className="text-sm text-gray-700 truncate flex-1">
                                {file.name}
                              </span>
                              <button
                                type="button"
                                className="ml-2 text-red-600 hover:text-red-800 text-sm"
                                onClick={() => {
                                  const newFiles =
                                    formData.additionalFiles.filter(
                                      (_, i) => i !== index
                                    );
                                  setFormData({
                                    ...formData,
                                    additionalFiles: newFiles,
                                  });
                                }}
                              >
                                Remove
                              </button>
                            </div>
                          ))}
                          <button
                            type="button"
                            className="text-sm text-red-600 hover:text-red-800"
                            onClick={() =>
                              setFormData({ ...formData, additionalFiles: [] })
                            }
                          >
                            Clear All
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Reason for Job Change */}
                  <div className="space-y-1 ">
                    <label
                      htmlFor="reasonForJobChange"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Reason for Job Change
                    </label>
                    <textarea
                      id="reasonForJobChange"
                      name="reasonForJobChange"
                      rows={2}
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.reasonForJobChange}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current Company */}
                  <div className="space-y-1">
                    <label
                      htmlFor="currentCompany"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Current Company
                    </label>
                    <input
                      type="text"
                      id="currentCompany"
                      name="currentCompany"
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.currentCompany}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current Job Position */}
                  <div className="space-y-1">
                    <label
                      htmlFor="currentJobPosition"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Current Job Position
                    </label>
                    <input
                      type="text"
                      id="currentJobPosition"
                      name="currentJobPosition"
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.currentJobPosition}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current Job Location */}
                  <div className="space-y-1">
                    <label
                      htmlFor="currentJobLocation"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Current Job Location
                    </label>
                    <input
                      type="text"
                      id="currentJobLocation"
                      name="currentJobLocation"
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.currentJobLocation}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Preferred Job Location */}
                  <div className="space-y-1">
                    <label
                      htmlFor="preferredJobLocation"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Preferred Job Location
                    </label>
                    <input
                      type="text"
                      id="preferredJobLocation"
                      name="preferredJobLocation"
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.preferredJobLocation}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Total Experience */}
                  <div className="space-y-1">
                    <label
                      htmlFor="totalExperience"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Total Experience
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <select
                        id="totalExperienceYears"
                        name="totalExperienceYears"
                        required
                        className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                        value={formData.totalExperienceYears}
                        onChange={handleInputChange}
                      >
                        <option value="">Years</option>
                        {yearOptions}
                      </select>
                      <select
                        id="totalExperienceMonths"
                        name="totalExperienceMonths"
                        required
                        className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                        value={formData.totalExperienceMonths}
                        onChange={handleInputChange}
                      >
                        <option value="">Months</option>
                        {monthOptions}
                      </select>
                    </div>
                  </div>

                  {/* Relevant Experience */}
                  <div className="space-y-1">
                    <label
                      htmlFor="relevantExperience"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Relevant Experience
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <select
                        id="relevantExperienceYears"
                        name="relevantExperienceYears"
                        required
                        className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                        value={formData.relevantExperienceYears}
                        onChange={handleInputChange}
                      >
                        <option value="">Years</option>
                        {yearOptions}
                      </select>
                      <select
                        id="relevantExperienceMonths"
                        name="relevantExperienceMonths"
                        required
                        className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                        value={formData.relevantExperienceMonths}
                        onChange={handleInputChange}
                      >
                        <option value="">Months</option>
                        {monthOptions}
                      </select>
                    </div>
                    {/* Experience validation message */}
                    {getExperienceValidationMessage() && (
                      <p className="mt-1 text-sm text-red-600">
                        ⚠️ {getExperienceValidationMessage()}
                      </p>
                    )}
                  </div>

                  {/* Serving Notice Period */}
                  <div className="space-y-1">
                    <label
                      htmlFor="noticePeriod"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Serving Notice Period
                    </label>
                    <select
                      id="noticePeriod"
                      name="noticePeriod"
                      required
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.noticePeriod}
                      onChange={handleInputChange}
                    >
                      <option value="">Select</option>
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                      <option value="Completed">Completed</option>
                    </select>
                  </div>

                  {/* Conditional fields for Serving Notice Period */}
                  {formData.noticePeriod === "Yes" && (
                    <div className="col-span-2 space-y-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <label
                            htmlFor="lastWorkingDate"
                            className="block text-sm font-medium text-blue-800"
                          >
                            * Last Working Date
                          </label>
                          <input
                            type="date"
                            id="lastWorkingDate"
                            name="lastWorkingDate"
                            required
                            className="block w-full px-2 py-1.5 border border-blue-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                            value={formData.lastWorkingDate}
                            onChange={handleInputChange}
                          />
                        </div>
                        <div className="space-y-1">
                          <label className="block text-sm font-medium text-blue-800">
                            Buyout Option
                          </label>
                          <div className="mt-1 flex items-center">
                            <input
                              type="checkbox"
                              id="noticePeriodBuyout"
                              name="noticePeriodBuyout"
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              checked={formData.noticePeriodBuyout}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  noticePeriodBuyout: e.target.checked,
                                })
                              }
                            />
                            <label
                              htmlFor="noticePeriodBuyout"
                              className="ml-2 text-sm text-blue-700"
                            >
                              Available for buyout
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {formData.noticePeriod === "No" && (
                    <div className="col-span-2 space-y-3 p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <label
                            htmlFor="noticePeriodDays"
                            className="block text-sm font-medium text-green-800"
                          >
                            * Notice Period
                          </label>
                          <select
                            id="noticePeriodDays"
                            name="noticePeriodDays"
                            required
                            className="block w-full pl-3 pr-10 py-2 text-base border border-green-300 focus:outline-none focus:ring-green-500 focus:border-green-500 text-xs rounded-md"
                            value={formData.noticePeriodDays}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Notice Period</option>
                            {noticePeriodOptions.map((option) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div className="space-y-1">
                          <label className="block text-sm font-medium text-green-800">
                            Buyout Option
                          </label>
                          <div className="mt-1 flex items-center">
                            <input
                              type="checkbox"
                              id="noticePeriodBuyout"
                              name="noticePeriodBuyout"
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-green-300 rounded"
                              checked={formData.noticePeriodBuyout}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  noticePeriodBuyout: e.target.checked,
                                })
                              }
                            />
                            <label
                              htmlFor="noticePeriodBuyout"
                              className="ml-2 text-sm text-green-700"
                            >
                              Available for buyout
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Holding Offer */}
                  <div className="space-y-1">
                    <label
                      htmlFor="joiningOffer"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Holding Offer
                    </label>
                    <select
                      id="joiningOffer"
                      name="joiningOffer"
                      required
                      className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs rounded-md"
                      value={formData.joiningOffer}
                      onChange={handleInputChange}
                    >
                      <option value="">Select</option>
                      {joiningOfferOptions.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>
                  {/* LinkedIn URL */}
                  <div className="space-y-1">
                    <label
                      htmlFor="linkedinUrl"
                      className="block text-sm font-medium text-gray-700"
                    >
                      LinkedIn URL
                    </label>
                    <input
                      type="url"
                      id="linkedinUrl"
                      name="linkedinUrl"
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.linkedinUrl}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Remarks */}
                  <div className="space-y-1">
                    <label
                      htmlFor="remarks"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Remarks
                    </label>
                    <textarea
                      id="remarks"
                      name="remarks"
                      rows={2}
                      className="block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                      value={formData.remarks}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Submit Button - Sticky to bottom, show when company is selected OR in talent pool mode */}
        {(selectedCompany || isTalentPoolMode) && (
          <div className="w-full p-4 flex justify-end bg-white shadow-top sticky bottom-0 border-t border-gray-200">
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md transition-colors ${isSubmitting
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700 text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                }`}
            >
              {isSubmitting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Processing...
                </>
              ) : editMode ? (
                "Update Candidate"
              ) : isTalentPoolMode ? (
                "Add New Candidate"
              ) : (
                "Register Candidate"
              )}
            </button>
          </div>
        )}
      </div>

      {/* Duplicate Warning Modal */}
      {showDuplicateWarning && duplicateData && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                <svg
                  className="h-6 w-6 text-yellow-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">
                Duplicate Candidate Found
              </h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  A candidate with similar details already exists in the system.
                </p>
                {duplicateData.jobIds && duplicateData.jobIds.length > 0 && (
                  <div className="mt-3 text-left">
                    <p className="text-sm font-medium text-gray-700 mb-2">
                      Existing entries:
                    </p>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {duplicateData.jobIds.map(
                        (jobId: number, index: number) => (
                          <div
                            key={jobId}
                            className="text-xs bg-gray-50 p-2 rounded"
                          >
                            <div>
                              <strong>Job ID:</strong> {jobId}
                            </div>
                            <div>
                              <strong>Client:</strong>{" "}
                              {duplicateData.clients?.[index] || "N/A"}
                            </div>
                            <div>
                              <strong>Profile:</strong>{" "}
                              {duplicateData.profiles?.[index] || "N/A"}
                            </div>
                            <div>
                              <strong>Status:</strong>{" "}
                              {duplicateData.status?.[index] || "N/A"}
                            </div>
                            <div>
                              <strong>Date:</strong>{" "}
                              {duplicateData.dates?.[index] || "N/A"}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex justify-center space-x-3">
                  <button
                    onClick={handleDuplicateWarningCancel}
                    className="px-4 py-2 bg-gray-300 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDuplicateWarningConfirm}
                    className="px-4 py-2 bg-blue-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    Proceed Anyway
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
