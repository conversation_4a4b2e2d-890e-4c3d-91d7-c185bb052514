import { useState } from "react";
import { toast } from "react-toastify";
import { Upload, <PERSON>, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";

// Types for the form
interface IssueFormData {
  title: string;
  type: string;
  severity: string;
  module: string;
  description: string;
  files: File[];
}

// Types for form validation errors
interface FormErrors {
  title?: string;
  type?: string;
  severity?: string;
  module?: string;
  description?: string;
  files?: string;
}

// Types for historical issues
interface HistoricalIssue {
  id: number;
  createdOn: string;
  lastUpdated: string;
  raisedBy: string;
  issueTitle: string;
  issueDescription: string;
  issueType: string;
  severity: string;
  status: string;
  module: string;
  assignedTo: string;
  resolutionNotes: string;
  comments: string;
}

// Mock data for historical issues
const mockHistoricalIssues: HistoricalIssue[] = [
  {
    id: 33,
    createdOn: "31/07/2025",
    lastUpdated: "01/01/1970",
    raisedBy: "Leonard Jereid Lambert",
    issueTitle:
      "Unable to edit the role in the JOB Assignments (after assigning)",
    issueDescription:
      "I have assigned a job but unable to edit the role in the JOB",
    issueType: "Other",
    severity: "Medium",
    status: "Open",
    module: "Job Assignments",
    assignedTo: "ATS Support Team",
    resolutionNotes: "-",
    comments: "Issue reported by Leonard. Team investigating the role editing functionality.",
  },
  {
    id: 51,
    createdOn: "31/07/2025",
    lastUpdated: "01/01/1970",
    raisedBy: "ManagerOne",
    issueTitle: "testing purpose",
    issueDescription: "testing purpose please ignore it",
    issueType: "Other",
    severity: "Low",
    status: "Open",
    module: "Other",
    assignedTo: "ATS Support Team",
    resolutionNotes: "-",
    comments: "Test issue created by ManagerOne for testing purposes.",
  },
  {
    id: 51,
    createdOn: "31/07/2025",
    lastUpdated: "01/01/1970",
    raisedBy: "ManagerOne",
    issueTitle: "dsgfdg",
    issueDescription: "sergasgadrg",
    issueType: "Performance",
    severity: "High",
    status: "Open",
    module: "Candidate",
    assignedTo: "ATS Support Team",
    resolutionNotes: "-",
    comments: "Performance issue reported. Requires immediate attention due to high severity.",
  },
];

export function HelpSupport() {
  const [formData, setFormData] = useState<IssueFormData>({
    title: "",
    type: "",
    severity: "",
    module: "",
    description: "",
    files: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showHistoricalIssues, setShowHistoricalIssues] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Handle form input changes
  const handleInputChange = (field: keyof IssueFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  // Handle file upload
  const handleFiles = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData((prev) => ({
        ...prev,
        files: [...prev.files, ...fileArray],
      }));
    }
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  // Validate form fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate title
    if (!formData.title.trim()) {
      newErrors.title = "Issue title is required";
    }

    // Validate type
    if (!formData.type) {
      newErrors.type = "Issue type is required";
    }

    // Validate severity
    if (!formData.severity) {
      newErrors.severity = "Severity is required";
    }

    // Validate module
    if (!formData.module) {
      newErrors.module = "Module is required";
    }

    // Validate description
    if (!formData.description.trim()) {
      newErrors.description = "Issue description is required";
    } else if (formData.description.trim().length < 10) {
      newErrors.description = "Description must be at least 10 characters long";
    }

    // Validate files (optional but show warning if none)
    if (formData.files.length === 0) {
      newErrors.files = "No files uploaded (optional but recommended)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fill in all required fields correctly');
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert files to base64
      const base64Files = await Promise.all(
        formData.files.map(async (file) => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = () => {
              resolve(reader.result as string);
            };
            reader.readAsDataURL(file);
          });
        })
      );

      // Prepare payload
      const payload = {
        screenshots: base64Files.map(img => img.split(',')[1]), // Remove data:image/...;base64, prefix
        issue_title: formData.title,
        issue_description: formData.description,
        issue_type: formData.type,
        severity: formData.severity,
        module_name: formData.module,
        userId: localStorage.getItem("user_id"),
      };

      // Submit to API
      const response = await fetch('http://**************:8081/auth_issue_report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log(result, "result")

      toast.success('Issue submitted successfully!');

      // Reset form and errors
      setFormData({
        title: "",
        type: "",
        severity: "",
        module: "",
        description: "",
        files: [],
      });
      setErrors({});

    } catch (error) {
      console.error('Error submitting issue:', error);
      toast.error('Failed to submit issue. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Remove file
  const removeFile = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index),
    }));
  };



  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-4 h-full w-full flex flex-col">
        <div className="flex items-center justify-between mb-4 flex-shrink-0">
          <h1 className="text-xl font-bold text-[var(--primary-dark)]">
            Help & Support
          </h1>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => setShowHistoricalIssues(!showHistoricalIssues)}
              className="flex items-center gap-2"
            >
              {showHistoricalIssues ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              {showHistoricalIssues ? "Hide" : "View"} Previous Issues
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          {showHistoricalIssues ? (
            <div className="w-full">
              <h2 className="text-lg font-semibold text-[var(--primary-dark)] mb-3">
                Historical Issues
              </h2>
              <AnimatedTableWrapper className="border border-gray-200 rounded-md overflow-hidden flex-1">
                <div
                  className="overflow-auto h-[calc(100vh-200px)]"
                  style={{ scrollbarGutter: "stable" }}
                >
                  <table
                    className="w-full table-fixed divide-y divide-gray-200"
                    style={{ minWidth: "1400px" }}
                  >
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-16">
                          ID
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-24">
                          Created On
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-24">
                          Last Updated
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-32">
                          Raised By
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-48">
                          Issue Title
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-48">
                          Issue Description
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-24">
                          Issue Type
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-20">
                          Severity
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-48">
                          Status
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-28">
                          Module
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-32">
                          Assigned To
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-32">
                          Resolution Notes
                        </th>
                        <th className="px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-48">
                          Comments
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {mockHistoricalIssues.map((issue, index) => (
                        <AnimatedTableRow
                          key={issue.id}
                          index={index}
                          className={`hover:bg-gray-50 ${index % 2 === 0 ? "bg-white" : "bg-gray-50"
                            }`}
                        >
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs text-gray-800  font-medium">
                            {issue.id}
                          </td>
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs text-gray-500">
                            {issue.createdOn}
                          </td>
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs text-gray-500">
                            {issue.lastUpdated}
                          </td>
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs text-gray-500">
                            {issue.raisedBy}
                          </td>
                          <td
                            className="px-3 py-2 bg-white text-xs text-gray-800 font-medium truncate"
                            title={issue.issueTitle}
                          >
                            {issue.issueTitle}
                          </td>
                          <td
                            className="px-3 py-2 bg-white text-xs text-gray-500 truncate"
                            title={issue.issueDescription}
                          >
                            {issue.issueDescription}
                          </td>
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${issue.issueType === "Other"
                                ? "bg-gray-100 text-gray-800"
                                : issue.issueType === "Performance"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-blue-100 text-blue-800"
                                }`}
                            >
                              {issue.issueType}
                            </span>
                          </td>
                          <td className="px-3 py-2 bg-white  whitespace-nowrap text-xs">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${issue.severity === "Low"
                                ? "bg-green-100 text-green-800"
                                : issue.severity === "Medium"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : issue.severity === "High"
                                    ? "bg-red-100 text-red-800"
                                    : "bg-purple-100 text-purple-800"
                                }`}
                            >
                              {issue.severity}
                            </span>
                          </td>
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold bg-green-100 text-green-800">
                              {issue.status}
                            </span>
                          </td>
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs text-gray-500">
                            {issue.module}
                          </td>
                          <td className="px-3 py-2 bg-white  whitespace-nowrap text-xs text-gray-500">
                            {issue.assignedTo}
                          </td>
                          <td className="px-3 py-2 bg-white whitespace-nowrap text-xs text-gray-500">
                            {issue.resolutionNotes}
                          </td>
                          <td
                            className="px-3 py-2 bg-white text-xs text-gray-600 truncate"
                            title={issue.comments}
                          >
                            {issue.comments}
                          </td>
                        </AnimatedTableRow>
                      ))}
                    </tbody>
                  </table>
                </div>
              </AnimatedTableWrapper>
            </div>
          ) : (
            <div className="max-w-4xl mx-auto form-container overflow-auto">
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* File Upload Area */}
                <div className="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div
                    className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragActive
                      ? "border-blue-400 bg-blue-50"
                      : "border-gray-300 hover:border-gray-400"
                      }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <Upload className="mx-auto h-10 w-10 text-gray-400 mb-3" />
                    <div className="text-base font-medium text-gray-900 mb-1">
                      Drop files here
                    </div>
                    <div className="text-sm text-gray-500 mb-3">
                      Drag files here • Paste images with Ctrl+V
                    </div>

                    <input
                      type="file"
                      multiple
                      onChange={(e) => handleFiles(e.target.files)}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>

                  {/* Display uploaded files */}
                  {formData.files.length > 0 && (
                    <div className="mt-3">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">
                        Uploaded Files:
                      </h4>
                      <div className="space-y-1">
                        {formData.files.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between bg-gray-50 p-2 rounded"
                          >
                            <span className="text-sm text-gray-700">
                              {file.name}
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                              className="text-red-600 hover:text-red-800"
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  {errors.files && (
                    <p className="mt-2 text-sm text-amber-600">{errors.files}</p>
                  )}
                </div>

                {/* Form Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  {/* Issue Title */}
                  <div>
                    <Label
                      htmlFor="title"
                      className="text-sm font-medium text-gray-700"
                    >
                      Issue Title <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="title"
                      type="text"
                      placeholder="Enter a brief title for the issue"
                      value={formData.title}
                      onChange={(e) =>
                        handleInputChange("title", e.target.value)
                      }
                      required
                      className={`mt-1 ${errors.title
                        ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        }`}
                    />
                    {errors.title && (
                      <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                    )}
                  </div>

                  {/* Issue Type */}
                  <div>
                    <Label
                      htmlFor="type"
                      className="text-sm font-medium text-gray-700"
                    >
                      Issue Type <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) =>
                        handleInputChange("type", value)
                      }
                    >
                      <SelectTrigger className={`mt-1 ${errors.type
                        ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        }`}>
                        <SelectValue placeholder="Select Issue Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Bug">Bug</SelectItem>
                        <SelectItem value="Enhancement">Enhancement</SelectItem>
                        <SelectItem value="Feature Request">
                          Feature Request
                        </SelectItem>
                        <SelectItem value="Data Error">Data Error</SelectItem>
                        <SelectItem value="Performance">Performance</SelectItem>
                        <SelectItem value="UI/UX">UI/UX</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className="mt-1 text-sm text-red-600">{errors.type}</p>
                    )}
                  </div>

                  {/* Severity */}
                  <div>
                    <Label
                      htmlFor="severity"
                      className="text-sm font-medium text-gray-700"
                    >
                      Severity <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.severity}
                      onValueChange={(value) =>
                        handleInputChange("severity", value)
                      }
                    >
                      <SelectTrigger className={`mt-1 ${errors.severity
                        ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        }`}>
                        <SelectValue placeholder="Select Severity" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Low">Low</SelectItem>
                        <SelectItem value="Medium">Medium</SelectItem>
                        <SelectItem value="High">High</SelectItem>
                        <SelectItem value="Critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.severity && (
                      <p className="mt-1 text-sm text-red-600">{errors.severity}</p>
                    )}
                  </div>

                  {/* Module */}
                  <div>
                    <Label
                      htmlFor="module"
                      className="text-sm font-medium text-gray-700"
                    >
                      Module <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.module}
                      onValueChange={(value) =>
                        handleInputChange("module", value)
                      }
                    >
                      <SelectTrigger className={`mt-1 ${errors.module
                        ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        }`}>
                        <SelectValue placeholder="Select Module" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Analytics">Analytics</SelectItem>
                        <SelectItem value="Calendar">Calendar</SelectItem>
                        <SelectItem value="Change Password">
                          Change Password
                        </SelectItem>
                        <SelectItem value="Candidate">Candidate</SelectItem>
                        <SelectItem value="Job Assignments">
                          Job Assignments
                        </SelectItem>
                        <SelectItem value="Job Listing">Job Listing</SelectItem>
                        <SelectItem value="Peer Assigned Profiles">
                          Peer Assigned Profiles
                        </SelectItem>
                        <SelectItem value="Profile Transfer">
                          Profile Transfer
                        </SelectItem>
                        <SelectItem value="Register Candidate">
                          Register Candidate
                        </SelectItem>
                        <SelectItem value="User Accounts">
                          User Accounts
                        </SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.module && (
                      <p className="mt-1 text-sm text-red-600">{errors.module}</p>
                    )}
                  </div>
                </div>

                {/* Issue Description */}
                <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <Label
                    htmlFor="description"
                    className="text-sm font-medium text-gray-700"
                  >
                    Issue Description <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="Describe the issue in detail... Include steps to reproduce, expected behavior, and actual behavior."
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange("description", e.target.value)
                    }
                    required
                    className={`mt-1 min-h-[100px] ${errors.description
                      ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                      : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      }`}
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                  )}
                </div>
              </form>
            </div>
          )}
        </div>

        {!showHistoricalIssues && (
          <div className="w-full p-3 flex justify-center text-center ">
            <Button
              type="button"
              onClick={handleSubmit}
              variant="default"
              disabled={isSubmitting}
              className="px-6 py-2 font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Submitting...
                </div>
              ) : (
                'RAISE ISSUE'
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
