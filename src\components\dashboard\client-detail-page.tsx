import { useState, useMemo, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Building2, ArrowLeft, Search, FileText, Target, Calendar, TrendingUp, Users, CheckCircle, XCircle, Clock } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectJobs } from "@/store/selectors/jobsSelectors";
import { selectClientCompanies } from "@/store/selectors/jobsSelectors";
import { selectCandidates, selectCandidatesLoading } from "@/store/selectors/candidatesSelectors";
import { fetchCandidates } from "@/store/slices/candidatesSlice";
import { useUser } from "@/contexts/user-context";

export function ClientDetailPage() {
  const navigate = useNavigate();
  const { clientId } = useParams();
  const dispatch = useAppDispatch();
  const { userId, userRole, userName } = useUser();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [candidateStatusFilter, setCandidateStatusFilter] = useState<string | null>(null);
  const itemsPerPage = 10;

  // Get real data from Redux store - ALL HOOKS MUST BE AT THE TOP
  const allJobs = useAppSelector(selectJobs) || [];
  const allClients = useAppSelector(selectClientCompanies) || [];
  const allCandidates = useAppSelector(selectCandidates) || [];
  const candidatesLoading = useAppSelector(selectCandidatesLoading);

  // Find the specific client - MOVED BEFORE CONDITIONAL LOGIC

  console.log('All clients:', allClients);
  const clientData = useMemo(() => {
    if (!allClients || !Array.isArray(allClients) || !clientId) {
      return null;
    }
    return allClients.find(client => client && client.id === clientId);
  }, [allClients, clientId]);

  // Get all jobs for this specific client - MOVED BEFORE CONDITIONAL LOGIC
  const clientJobs = useMemo(() => {
    if (!clientId || !allJobs || !Array.isArray(allJobs) || allJobs.length === 0) return [];

    // Use case-insensitive matching for better compatibility
    return allJobs.filter(job =>
      job && job.client && typeof job.client === 'string' &&
      job.client.trim().toLowerCase() === clientId.toLowerCase()
    );
  }, [clientId, allJobs]);

  // Filter jobs based on search term and status filter - MOVED BEFORE CONDITIONAL LOGIC
  const filteredJobs = useMemo(() => {
    let filtered = clientJobs;

    // Apply status filter first
    if (statusFilter) {
      filtered = filtered.filter(job => job.job_status === statusFilter);
    }

    // Then apply search filter
    if (searchTerm) {
      filtered = filtered.filter(job =>
        job && job.id && job.id.toString().includes(searchTerm) ||
        (job && job.role && typeof job.role === 'string' && job.role.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (job && job.job_status && typeof job.job_status === 'string' && job.job_status.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (job && job.recruiter && typeof job.recruiter === 'string' && job.recruiter.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (job && job.management && typeof job.management === 'string' && job.management.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    return filtered;
  }, [clientJobs, searchTerm, statusFilter]);

  // Calculate client statistics - MOVED BEFORE CONDITIONAL LOGIC
  const clientStats = useMemo(() => {
    if (!clientJobs || !Array.isArray(clientJobs) || clientJobs.length === 0) {
      return { active: 0, closed: 0, onHold: 0, total: 0 };
    }

    return {
      active: clientJobs.filter(job => job && job.job_status === 'Active').length,
      closed: clientJobs.filter(job => job && job.job_status === 'Closed').length,
      onHold: clientJobs.filter(job => job && (job.job_status === 'Hold' || job.job_status === 'On Hold')).length,
      total: clientJobs.length
    };
  }, [clientJobs]);

  // Pagination calculations - MOVED BEFORE CONDITIONAL LOGIC
  const totalPages = Math.ceil((filteredJobs && Array.isArray(filteredJobs) ? filteredJobs.length : 0) / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedJobs = (filteredJobs && Array.isArray(filteredJobs)) ? filteredJobs.slice(startIndex, startIndex + itemsPerPage) : [];

  // Reset to first page when search or status filter changes - MOVED BEFORE CONDITIONAL LOGIC
  useMemo(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Fetch candidates when component mounts
  useEffect(() => {
    if (userId && userRole && userName) {
      dispatch(fetchCandidates({ userId, userType: userRole, userName }));
    }
  }, [dispatch, userId, userRole, userName]);

  // Get real candidate analytics for the selected job
  const getJobAnalytics = (job: any) => {
    try {
      if (!allCandidates || !Array.isArray(allCandidates) || allCandidates.length === 0 || !job) {
        console.log('No candidates available for analytics or invalid job');
        return {
          totalCandidates: 0,
          selected: 0,
          rejected: 0,
          inProgress: 0
        };
      }

      console.log('Job for analytics:', {
        job_id: job.job_id,
        id: job.id,
        role: job.role
      });
      console.log('Sample candidate:', allCandidates[0]);

      // Ensure job has required properties
      if (!job.job_id && !job.id) {
        console.log('Job missing required ID properties:', job);
        return {
          totalCandidates: 0,
          selected: 0,
          rejected: 0,
          inProgress: 0
        };
      }

      // Filter candidates by job ID - try multiple matching strategies
      const jobCandidates = allCandidates.filter(candidate => {
        try {
          // Skip candidates with missing required data
          if (!candidate || !candidate.jobId) {
            console.log('Skipping candidate with missing data:', candidate);
            return false;
          }

          // Try exact match first
          if (candidate.jobId === job.job_id) return true;
          if (candidate.jobId === job.id) return true;

          // Try string comparison
          if (String(candidate.jobId) === String(job.job_id)) return true;
          if (String(candidate.jobId) === String(job.id)) return true;

          // Profile matching removed - only exact job ID matches allowed

          return false;
        } catch (error) {
          console.error('Error filtering candidate:', error, candidate);
          return false;
        }
      });

      console.log(`Found ${jobCandidates.length} candidates for job ${job.job_id}/${job.id}:`, jobCandidates);

      // Count candidates by status
      const totalCandidates = jobCandidates.length;
      const selected = jobCandidates.filter(c => {
        try {
          return c && c.status && typeof c.status === 'string' && (
            c.status.toLowerCase().includes('hired') ||
            c.status.toLowerCase().includes('onboarded') ||
            c.status.toLowerCase().includes('offer')
          );
        } catch (error) {
          console.error('Error checking selected status:', error, c);
          return false;
        }
      }).length;
      const rejected = jobCandidates.filter(c => {
        try {
          return c && c.status && typeof c.status === 'string' && (
            c.status.toLowerCase().includes('rejected') ||
            c.status.toLowerCase().includes('dropped')
          );
        } catch (error) {
          console.error('Error checking rejected status:', error, c);
          return false;
        }
      }).length;
      const inProgress = totalCandidates - selected - rejected;

      return {
        totalCandidates,
        selected,
        rejected,
        inProgress
      };
    } catch (error) {
      console.error('Error in getJobAnalytics:', error);
      return {
        totalCandidates: 0,
        selected: 0,
        rejected: 0,
        inProgress: 0
      };
    }
  };

  const handleBackToClients = () => {
    navigate(`/${userRole}/clients`);
  };

  const handleJobClick = (job: any) => {
    setSelectedJob(job);
    setCandidateStatusFilter(null); // Reset candidate filter when switching jobs
  };

  const handleBackToJobs = () => {
    setSelectedJob(null);
  };

  const handleStatusFilter = (status: string | null) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  const handleCandidateStatusFilter = (status: string | null) => {
    setCandidateStatusFilter(status);
  };

  // Check if data is still loading - MOVED AFTER ALL HOOKS
  const isDataLoading = !allJobs || !Array.isArray(allJobs) || allJobs.length === 0 ||
    !allClients || !Array.isArray(allClients) || allClients.length === 0 ||
    candidatesLoading;

  // Show loading state if data is not ready
  if (isDataLoading) {
    return (
      <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Loading client data...</h3>
            <p className="mt-2 text-sm text-gray-500">
              Jobs: {allJobs && Array.isArray(allJobs) ? allJobs.length : 0} | Clients: {allClients && Array.isArray(allClients) ? allClients.length : 0}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If client not found, show error
  if (!clientData) {
    return (
      <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Client not found</h3>
            <p className="mt-1 text-sm text-gray-500">The client you're looking for doesn't exist.</p>
            <div className="mt-6">
              <button
                onClick={handleBackToClients}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Clients
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackToClients}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Clients
            </button>

            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search by Job ID, role, status, recruiter, or management..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <h1 className="text-3xl font-bold text-gray-900">{clientData.name}</h1>
          <p className="text-gray-600 mt-2">
            Detailed overview of all jobs and recruitment activities for this client.
            {searchTerm && ` Showing ${filteredJobs.length} results for "${searchTerm}"`}
          </p>
        </div>

        {/* Job Detail View */}
        {selectedJob && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <button
                onClick={handleBackToJobs}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Jobs
              </button>
              <h2 className="text-2xl font-bold text-gray-900">
                {selectedJob.role} - {selectedJob.location}
              </h2>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${selectedJob.job_status === 'Active' ? 'bg-green-100 text-green-800' :
                  selectedJob.job_status === 'Closed' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                  {selectedJob.job_status}
                </span>
              </div>
            </div>

            {/* Job Analytics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div
                className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${candidateStatusFilter === null
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-purple-300'
                  }`}
                onClick={() => handleCandidateStatusFilter(null)}
                title="Click to show all candidates"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Candidates</p>
                    <p className="text-3xl font-bold text-purple-600">{getJobAnalytics(selectedJob).totalCandidates}</p>
                  </div>
                  <div className="p-3 rounded-full bg-purple-500">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>

              <div
                className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${candidateStatusFilter === 'selected'
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-green-300'
                  }`}
                onClick={() => handleCandidateStatusFilter('selected')}
                title="Click to filter by selected candidates"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Selected</p>
                    <p className="text-3xl font-bold text-green-600">{getJobAnalytics(selectedJob).selected}</p>
                  </div>
                  <div className="p-3 rounded-full bg-green-500">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>

              <div
                className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${candidateStatusFilter === 'rejected'
                  ? 'border-red-500 bg-red-50'
                  : 'border-gray-200 hover:border-red-300'
                  }`}
                onClick={() => handleCandidateStatusFilter('rejected')}
                title="Click to filter by rejected candidates"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium text-gray-600">Rejected</p>
                      {candidateStatusFilter === 'rejected' && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Active Filter
                        </span>
                      )}
                    </div>
                    <p className="text-3xl font-bold text-red-600">{getJobAnalytics(selectedJob).rejected}</p>
                  </div>
                  <div className="p-3 rounded-full bg-red-500">
                    <XCircle className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>

              <div
                className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${candidateStatusFilter === 'inProgress'
                  ? 'border-yellow-500 bg-yellow-50'
                  : 'border-gray-200 hover:border-yellow-300'
                  }`}
                onClick={() => handleCandidateStatusFilter('inProgress')}
                title="Click to filter by in-progress candidates"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium text-gray-600">In Progress</p>
                      {candidateStatusFilter === 'inProgress' && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Active Filter
                        </span>
                      )}
                    </div>
                    <p className="text-3xl font-bold text-yellow-600">{getJobAnalytics(selectedJob).inProgress}</p>
                  </div>
                  <div className="p-3 rounded-full bg-yellow-500">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md border border-gray-200 mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Job Details</h3>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-600 mb-2">Job Information</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Job ID:</span>
                        <span className="text-sm font-medium text-gray-900">#{selectedJob.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Role:</span>
                        <span className="text-sm font-medium text-gray-900">{selectedJob.role}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Location:</span>
                        <span className="text-sm font-medium text-gray-900">{selectedJob.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Job Type:</span>
                        <span className="text-sm font-medium text-gray-900">{selectedJob.job_type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Status:</span>
                        <span className="text-sm font-medium text-gray-900">{selectedJob.job_status}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-600 mb-2">Recruitment Details</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Recruiter:</span>
                        <span className="text-sm font-medium text-gray-900">{selectedJob.recruiter || (selectedJob.management ? `${selectedJob.management} (Manager)` : 'Not Assigned')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Positions:</span>
                        <span className="text-sm font-medium text-gray-900">{selectedJob.no_of_positions}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Posted Date:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {new Date(selectedJob.date_created).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Client:</span>
                        <span className="text-sm font-medium text-gray-900">{selectedJob.client}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Candidates Table */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Candidates for this Job</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {candidateStatusFilter === null ? 'Showing all candidates associated with this position' :
                        candidateStatusFilter === 'selected' ? 'Showing selected candidates (hired, onboarded, offer)' :
                          candidateStatusFilter === 'rejected' ? 'Showing rejected candidates (rejected, dropped)' :
                            'Showing in-progress candidates'}
                      {candidateStatusFilter && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Filtered by: {candidateStatusFilter === 'selected' ? 'Selected' :
                            candidateStatusFilter === 'rejected' ? 'Rejected' : 'In Progress'}
                        </span>
                      )}
                    </p>
                  </div>
                  {candidateStatusFilter && (
                    <button
                      onClick={() => handleCandidateStatusFilter(null)}
                      className="text-sm text-gray-500 hover:text-gray-700 underline"
                    >
                      Clear Filter
                    </button>
                  )}
                </div>
              </div>

              {(() => {
                const analytics = getJobAnalytics(selectedJob);
                if (analytics.totalCandidates === 0) {
                  return (
                    <div className="text-center py-12">
                      <Users className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No candidates found</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        No candidates have been assigned to this job yet.
                      </p>
                    </div>
                  );
                }

                // Get the actual candidates for this job
                let jobCandidates = allCandidates.filter(candidate => {
                  try {
                    if (!candidate || !candidate.jobId) return false;

                    // Try exact match first
                    if (candidate.jobId === selectedJob.job_id) return true;
                    if (candidate.jobId === selectedJob.id) return true;

                    // Try string comparison
                    if (String(candidate.jobId) === String(selectedJob.job_id)) return true;
                    if (String(candidate.jobId) === String(selectedJob.id)) return true;

                    // Profile matching removed - only exact job ID matches allowed

                    return false;
                  } catch (error) {
                    console.error('Error filtering candidate:', error, candidate);
                    return false;
                  }
                });

                // Log the filtering results for debugging
                console.log('Job filtering debug:', {
                  selectedJobId: selectedJob.job_id || selectedJob.id,
                  selectedJobRole: selectedJob.role,
                  totalCandidates: allCandidates.length,
                  filteredCandidates: jobCandidates.length,
                  candidates: jobCandidates.map(c => ({
                    id: c.id,
                    name: c.name,
                    jobId: c.jobId,
                    status: c.status
                  }))
                });

                // Apply status filter if active
                if (candidateStatusFilter) {
                  jobCandidates = jobCandidates.filter(candidate => {
                    try {
                      if (!candidate || !candidate.status) return false;

                      const status = candidate.status.toLowerCase();

                      switch (candidateStatusFilter) {
                        case 'selected':
                          return status.includes('hired') || status.includes('onboarded') || status.includes('offer');
                        case 'rejected':
                          return status.includes('rejected') || status.includes('dropped');
                        case 'inProgress':
                          return !(status.includes('hired') || status.includes('onboarded') || status.includes('offer') ||
                            status.includes('rejected') || status.includes('dropped'));
                        default:
                          return true;
                      }
                    } catch (error) {
                      console.error('Error filtering by status:', error, candidate);
                      return false;
                    }
                  });
                }

                return (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job ID</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recruiter</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {jobCandidates.map((candidate) => (
                          <tr key={candidate.id} className="hover:bg-gray-50">
                            <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              #{candidate.jobId}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">
                                {candidate.name}
                              </div>
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                              {candidate.email || 'N/A'}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                              {candidate.phone || 'N/A'}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                              {candidate.recruiter || (candidate.management ? `${candidate.management} (Manager)` : 'N/A')}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${candidate.status && candidate.status.toLowerCase().includes('hired') ? 'bg-green-100 text-green-800' :
                                candidate.status && candidate.status.toLowerCase().includes('rejected') ? 'bg-red-100 text-red-800' :
                                  candidate.status && candidate.status.toLowerCase().includes('onboarded') ? 'bg-blue-100 text-blue-800' :
                                    candidate.status && candidate.status.toLowerCase().includes('offer') ? 'bg-purple-100 text-purple-800' :
                                      'bg-yellow-100 text-yellow-800'
                                }`}>
                                {candidate.status || 'Unknown'}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                );
              })()}
            </div>

            {/* Job Details Table */}
          </div>
        )}

        {/* Client Summary Stats - Only show when no job is selected */}
        {!selectedJob && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div
              className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${statusFilter === null
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-blue-300'
                }`}
              onClick={() => handleStatusFilter(null)}
              title="Click to show all jobs"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Jobs</p>
                  <p className="text-3xl font-bold text-blue-600">{clientStats.total}</p>
                </div>
                <div className="p-3 rounded-full bg-blue-500">
                  <Target className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <div
              className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${statusFilter === 'Active'
                ? 'border-green-500 bg-green-50'
                : 'border-green-300 hover:border-green-400'
                }`}
              onClick={() => handleStatusFilter('Active')}
              title="Click to filter by Active jobs"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Jobs</p>
                  <p className="text-3xl font-bold text-green-600">{clientStats.active}</p>
                </div>
                <div className="p-3 rounded-full bg-green-500">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <div
              className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${statusFilter === 'Hold' || statusFilter === 'On Hold'
                ? 'border-yellow-500 bg-yellow-50'
                : 'border-yellow-300 hover:border-yellow-400'
                }`}
              onClick={() => handleStatusFilter('Hold')}
              title="Click to filter by On Hold jobs"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">On Hold</p>
                  <p className="text-3xl font-bold text-yellow-600">{clientStats.onHold}</p>
                </div>
                <div className="p-3 rounded-full bg-yellow-500">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <div
              className={`bg-white rounded-lg shadow-md p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${statusFilter === 'Closed'
                ? 'border-gray-500 bg-gray-50'
                : 'border-gray-300 hover:border-gray-400'
                }`}
              onClick={() => handleStatusFilter('Closed')}
              title="Click to filter by Closed jobs"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Closed Jobs</p>
                  <p className="text-3xl font-bold text-gray-600">{clientStats.closed}</p>
                </div>
                <div className="p-3 rounded-full bg-gray-500">
                  <FileText className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Jobs Table - Only show when no job is selected */}
        {!selectedJob && (
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">All Jobs for {clientData.name}</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Showing {filteredJobs.length} of {clientJobs.length} total jobs
                    {statusFilter && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Filtered by: {statusFilter}
                      </span>
                    )}
                  </p>
                </div>
                {statusFilter && (
                  <button
                    onClick={() => handleStatusFilter(null)}
                    className="text-sm text-gray-500 hover:text-gray-700 underline"
                  >
                    Clear Filter
                  </button>
                )}
              </div>
            </div>

            {filteredJobs.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No jobs found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? `No jobs match "${searchTerm}"` : "This client has no jobs yet."}
                </p>
              </div>
            ) : (
              <div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job ID</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Type</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recruiter</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Positions</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Posted Date</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {paginatedJobs.map((job) => (
                        <tr
                          key={job.id}
                          className="hover:bg-gray-50 cursor-pointer transition-colors"
                          onClick={() => handleJobClick(job)}
                        >
                          <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{job.id}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{job.role}</div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{job.location}</td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{job.job_type}</td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${job.job_status === 'Active' ? 'bg-green-100 text-green-800' :
                              job.job_status === 'Closed' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                              {job.job_status}
                            </span>
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500 max-w-[120px] truncate" title={job.recruiter || (job.management ? `${job.management} (Manager)` : 'Not Assigned')}>
                            {job.recruiter || (job.management ? `${job.management} (Manager)` : 'Not Assigned')}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{job.no_of_positions}</td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(job.date_created).toLocaleDateString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 bg-white p-2 rounded-md border border-gray-200">
                      <div className="text-xs sm:text-sm text-gray-600">
                        Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredJobs.length)} of {filteredJobs.length} results
                      </div>

                      <div className="w-full sm:w-auto flex justify-center sm:justify-end">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                            className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Previous
                          </button>

                          {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                            <button
                              key={page}
                              onClick={() => setCurrentPage(page)}
                              className={`px-3 py-1 border rounded-md text-sm font-medium ${currentPage === page
                                ? 'bg-blue-500 text-white border-blue-500'
                                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                                }`}
                            >
                              {page}
                            </button>
                          ))}

                          <button
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                            className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Next
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
