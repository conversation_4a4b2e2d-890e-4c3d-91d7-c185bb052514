import { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";

interface SearchableDropdownProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  suggestions: string[];
  placeholder?: string;
  required?: boolean;
  isNewMode?: boolean;
  onToggleNewMode?: () => void;
  newModeText?: string;
  selectExistingText?: string;

  name: string;
  // For simple mode (like recruiters) - no toggle, just searchable input
  simpleMode?: boolean;
  borderColor?: string;
}

export function SearchableDropdown({
  label,
  value,
  onChange,
  suggestions,
  placeholder = "Type to search...",
  required = false,
  isNewMode = false,
  onToggleNewMode = () => { },
  newModeText = "",
  selectExistingText = "",

  name,
  simpleMode = false,
  borderColor = "blue",
}: SearchableDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter suggestions based on search term
  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (isNewMode || simpleMode) {
      onChange(newValue);
      if (simpleMode) {
        setSearchTerm(newValue);
        setIsOpen(true);
      }
    } else {
      setSearchTerm(newValue);
      setIsOpen(true);
      // Allow editing even after selection
      if (newValue !== value) {
        onChange(newValue);
      }
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setSearchTerm(suggestion); // Keep the search term in sync with selection
    setIsOpen(false);
  };

  const handleInputFocus = () => {
    if (!isNewMode || simpleMode) {
      // Initialize search term with current value when focusing
      if (value && !searchTerm) {
        setSearchTerm(value);
      }
      setIsOpen(true);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  return (
    <div className="space-y-1 w-full">
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700"
        >
          * {label}:
        </label>
      )}

      {simpleMode ? (
        <div className="relative" ref={dropdownRef}>
          <input
            ref={inputRef}
            type="text"
            id={name}
            name={name}
            required={required}
            className={`w-full px-3 py-2.5 pr-8 border rounded-md focus:outline-none focus:ring-2 focus:ring-${borderColor}-500 focus:border-transparent ${borderColor === 'blue' ? 'border-blue-300' :
              borderColor === 'green' ? 'border-green-300' :
                borderColor === 'purple' ? 'border-purple-300' :
                  borderColor === 'yellow' ? 'border-yellow-300' :
                    'border-blue-300'
              }`}
            value={value}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            autoComplete="off"
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 flex items-center px-2 text-gray-400"
            onClick={() => setIsOpen(!isOpen)}
          >
            <ChevronDown className="h-4 w-4" />
          </button>

          {isOpen && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
              {filteredSuggestions.length > 0 ? (
                filteredSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    {suggestion}
                  </button>
                ))
              ) : (
                <div className="px-3 py-2 text-gray-500 text-sm">
                  No matches found
                </div>
              )}
            </div>
          )}
        </div>
      ) : isNewMode ? (
        <div className="space-y-2">
          <input
            ref={inputRef}
            type="text"
            id={name}
            name={name}
            required={required}
            className={`w-full px-3 py-2.5 border rounded-md focus:outline-none focus:ring-2 focus:ring-${borderColor}-500 focus:border-transparent ${borderColor === 'blue' ? 'border-blue-300' :
              borderColor === 'green' ? 'border-green-300' :
                borderColor === 'purple' ? 'border-purple-300' :
                  borderColor === 'yellow' ? 'border-yellow-300' :
                    'border-blue-300'
              }`}
            value={value}
            onChange={handleInputChange}
            placeholder={placeholder}
          />
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-600">{newModeText}</span>
            <button
              type="button"
              onClick={onToggleNewMode}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {selectExistingText}
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-2" ref={dropdownRef}>
          <div className="relative">
            <input
              ref={inputRef}
              type="text"
              id={name}
              name={name}
              required={required}
              className={`w-full px-3 py-2.5 pr-8 border rounded-md focus:outline-none focus:ring-2 focus:ring-${borderColor}-500 focus:border-transparent ${borderColor === 'blue' ? 'border-blue-300' :
                borderColor === 'green' ? 'border-green-300' :
                  borderColor === 'purple' ? 'border-purple-300' :
                    borderColor === 'yellow' ? 'border-yellow-300' :
                      'border-blue-300'
                }`}
              value={searchTerm || value}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              autoComplete="off"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center px-1.5 text-gray-400"
              onClick={() => setIsOpen(!isOpen)}
            >
              <ChevronDown className="h-3 w-3" />
            </button>

            {isOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                {filteredSuggestions.length > 0 ? (
                  <>
                    {filteredSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        type="button"
                        className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        {suggestion}
                      </button>
                    ))}
                    {/* Show "Add new" option at the bottom when there are suggestions */}
                    <div className="border-t border-gray-200">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          onToggleNewMode();
                        }}
                        className="w-full px-3 py-2 text-center text-blue-600 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none text-sm font-medium"
                      >
                        "{searchTerm}" as new {label.toLowerCase()}
                      </button>
                    </div>
                  </>
                ) : searchTerm && searchTerm.trim() !== '' ? (
                  /* Show "Add new" option when no matches found and user has typed something */
                  <div className="px-3 py-2">
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onToggleNewMode();
                      }}
                      className="w-full px-3 py-2 text-center text-blue-600 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none text-sm font-medium border border-blue-200 rounded-md bg-blue-50"
                    >
                      "{searchTerm}" as new {label.toLowerCase()}
                    </button>
                  </div>
                ) : (
                  <div className="px-3 py-2 text-gray-500 text-sm">
                    Start typing to search...
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
