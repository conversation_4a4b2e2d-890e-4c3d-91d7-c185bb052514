import { useState, useEffect } from 'react';
import { X, Target, User, Save } from 'lucide-react';
import { toast } from 'react-toastify';

export interface RecruiterTargets {
  recruiterId: string;
  recruiterName: string;
  timePeriod: 'weekly';
  targets: {
    profilesSubmitted: { min: number; max: number };
    interviewsScheduled: { min: number; max: number };
    offers: { min: number; max: number };
    joiners: { min: number; max: number };
  };
  lastUpdated: string;
}

interface TargetManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  recruiter: { id: string; name: string } | null;
  onSave: (targets: RecruiterTargets) => void;
  existingTargets?: RecruiterTargets;
}

export function TargetManagementModal({
  isOpen,
  onClose,
  recruiter,
  onSave,
  existingTargets
}: TargetManagementModalProps) {
  const [timePeriod, setTimePeriod] = useState<'weekly'>('weekly');
  const [isEditing, setIsEditing] = useState(false);
  const [targets, setTargets] = useState({
    profilesSubmitted: { min: 0, max: 0 },
    interviewsScheduled: { min: 0, max: 0 },
    offers: { min: 0, max: 0 },
    joiners: { min: 0, max: 0 }
  });

  // Always in editing mode - no toggle needed

  useEffect(() => {
    if (existingTargets) {
      setTimePeriod('weekly'); // Always weekly
      setTargets(existingTargets.targets);
    } else {
      // Initialize with zero targets if none exist
      setTargets({
        profilesSubmitted: { min: 0, max: 0 },
        interviewsScheduled: { min: 0, max: 0 },
        offers: { min: 0, max: 0 },
        joiners: { min: 0, max: 0 }
      });
    }
    // Always start in view mode
    setIsEditing(false);
  }, [existingTargets]);

  const handleInputChange = (field: string, type: 'min' | 'max', value: string) => {
    const numValue = parseInt(value) || 0;
    setTargets(prev => ({
      ...prev,
      [field]: {
        ...prev[field as keyof typeof prev],
        [type]: numValue
      }
    }));
  };



  const handleToggleEdit = () => {
    setIsEditing(!isEditing);
  };

  const handleSave = () => {
    if (!recruiter) return;

    // Validate targets
    const hasInvalidTargets = Object.values(targets).some(target =>
      target.min > target.max
    );

    if (hasInvalidTargets) {
      toast.error('Invalid targets: Minimum values cannot be greater than maximum values');
      return;
    }

    const recruiterTargets: RecruiterTargets = {
      recruiterId: recruiter.id,
      recruiterName: recruiter.name,
      timePeriod: timePeriod,
      targets,
      lastUpdated: new Date().toISOString()
    };

    onSave(recruiterTargets);
    toast.success(`Targets updated for ${recruiter.name}`);
    setIsEditing(false); // Switch back to view mode
  };

  if (!isOpen || !recruiter) return null;

  return (
    <div className="fixed inset-0 bg-transparent backdrop-blur-sm flex items-center justify-center z-50 p-2">
      <div className="bg-white rounded-lg shadow-xl max-w-xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="p-0.5 bg-blue-100 rounded">
              <Target className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Set Targets for Recruiter</h2>
              <p className="text-xs text-gray-600">{recruiter.name}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 pb-6 space-y-4 -mt-3">
          {/* Time Period Info */}
          <div className="bg-blue-50 rounded p-3">
            <div className="flex items-center">
              <div>
                <div className="text-xs text-blue-600 mt-1">⚠️ Targets are measured on a weekly basis</div>
              </div>
            </div>
          </div>

          {/* Target Fields */}
          <div className="space-y-3 -mt-2">
            {/* Profile Submission */}
            <div className="bg-gray-50 rounded p-2">
              <div className="flex items-center justify-between mb-1">
                <label className="text-xs font-medium text-gray-700 flex items-center">
                  <User className="h-3 w-3 mr-1 text-blue-500" />
                  Profile Submission
                </label>
                <span className="text-xs text-gray-500">Weekly</span>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.profilesSubmitted.min}
                    onChange={(e) => handleInputChange('profilesSubmitted', 'min', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.profilesSubmitted.max}
                    onChange={(e) => handleInputChange('profilesSubmitted', 'max', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
              </div>
            </div>

            {/* Interview Scheduling */}
            <div className="bg-gray-50 rounded p-1">
              <div className="flex items-center justify-between mb-2">
                <label className="text-xs font-medium text-gray-700 flex items-center">
                  <Target className="h-3 w-3 mr-1 text-green-500" />
                  Interview Scheduling
                </label>
                <span className="text-xs text-gray-500">Weekly</span>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.interviewsScheduled.min}
                    onChange={(e) => handleInputChange('interviewsScheduled', 'min', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.interviewsScheduled.max}
                    onChange={(e) => handleInputChange('interviewsScheduled', 'max', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
              </div>
            </div>

            {/* Offer Generation */}
            <div className="bg-gray-50 rounded p-1">
              <div className="flex items-center justify-between mb-2">
                <label className="text-xs font-medium text-gray-700 flex items-center">
                  <Target className="h-3 w-3 mr-1 text-purple-500" />
                  Offer Generation
                </label>
                <span className="text-xs text-gray-500">Monthly</span>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.offers.min}
                    onChange={(e) => handleInputChange('offers', 'min', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.offers.max}
                    onChange={(e) => handleInputChange('offers', 'max', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
              </div>
            </div>

            {/* Joiner Placement */}
            <div className="bg-gray-50 rounded p-1">
              <div className="flex items-center justify-between mb-2">
                <label className="text-xs font-medium text-gray-700 flex items-center">
                  <Target className="h-3 w-3 mr-1 text-red-500" />
                  Joiners
                </label>
                <span className="text-xs text-gray-500">Monthly</span>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.joiners.min}
                    onChange={(e) => handleInputChange('joiners', 'min', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.joiners.max}
                    onChange={(e) => handleInputChange('joiners', 'max', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-2 py-1.5 border rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent ${isEditing
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-100 cursor-not-allowed'
                      }`}
                  />
                </div>
              </div>
            </div>


          </div>
        </div>

        {/* Footer with Edit and Save buttons */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex justify-end space-x-3">
            {isEditing ? (
              <button
                onClick={handleSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 transition-colors font-medium"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Targets
              </button>
            ) : (
              <button
                onClick={handleToggleEdit}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors font-medium"
              >
                <Target className="h-4 w-4 mr-2" />
                Edit Targets
              </button>
            )}
          </div>
        </div>

      </div>
    </div>
  );
}
