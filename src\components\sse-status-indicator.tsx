import React, { useEffect, useState } from 'react';
import { sseService, type SSEEvent } from '@/services/sse';

interface SSEStatusIndicatorProps {
  userId: string;
  userRole: string;
  className?: string;
}

export const SSEStatusIndicator: React.FC<SSEStatusIndicatorProps> = ({
  userId,
  userRole,
  className = ''
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null);
  const [eventCount, setEventCount] = useState(0);

  useEffect(() => {
    // Connect to SSE
    sseService.connect(userId, userRole);

    // Add event listener
    const handleSSEEvent = (event: SSEEvent) => {
      setLastEvent(event);
      setEventCount(prev => prev + 1);
      
      // Log the event for debugging
      console.log('SSE Event received:', event);
      
      // Handle different event types
      switch (event.type) {
        case 'candidate_data_update':
          console.log('Complete candidate data updated:', event.data);
          break;
        case 'candidate_update':
          console.log('Candidate status updated:', event.data);
          break;
        case 'job_data_update':
          console.log('Complete job data updated:', event.data);
          break;
        case 'job_update':
          console.log('Job status updated:', event.data);
          break;
        case 'profile_data_update':
          console.log('Complete profile data updated:', event.data);
          break;
        case 'profile_update':
          console.log('Profile status updated:', event.data);
          break;
        default:
          console.log('Other SSE event:', event);
      }
    };

    sseService.addEventListener(handleSSEEvent);

    // Check connection status periodically
    const statusInterval = setInterval(() => {
      setIsConnected(sseService.getConnectionStatus());
    }, 1000);

    // Cleanup
    return () => {
      clearInterval(statusInterval);
      sseService.removeEventListener(handleSSEEvent);
      sseService.disconnect();
    };
  }, [userId, userRole]);

  const getStatusColor = () => {
    return isConnected ? 'bg-green-500' : 'bg-red-500';
  };

  const getStatusText = () => {
    return isConnected ? 'Connected' : 'Disconnected';
  };

  const getEventTypeDisplay = (type: string) => {
    switch (type) {
      case 'candidate_data_update':
        return 'Candidate Data';
      case 'candidate_update':
        return 'Candidate Status';
      case 'job_data_update':
        return 'Job Data';
      case 'job_update':
        return 'Job Status';
      case 'profile_data_update':
        return 'Profile Data';
      case 'profile_update':
        return 'Profile Status';
      default:
        return type;
    }
  };

  return (
    <div className={`p-4 border rounded-lg bg-white shadow-sm ${className}`}>
      <div className="flex items-center gap-2 mb-3">
        <div className={`w-3 h-3 rounded-full ${getStatusColor()}`}></div>
        <span className="font-medium">SSE Status: {getStatusText()}</span>
      </div>
      
      <div className="text-sm text-gray-600 mb-2">
        Events received: <span className="font-medium">{eventCount}</span>
      </div>

      {lastEvent && (
        <div className="border-t pt-3 mt-3">
          <div className="text-sm">
            <div className="font-medium text-gray-700 mb-1">Last Event:</div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">Type:</span>
                <span className="ml-1 font-medium">
                  {getEventTypeDisplay(lastEvent.type)}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Time:</span>
                <span className="ml-1">
                  {new Date(lastEvent.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
            
            {lastEvent.data && (
              <div className="mt-2">
                <div className="text-gray-500 text-xs mb-1">Data:</div>
                <div className="bg-gray-50 p-2 rounded text-xs font-mono max-h-20 overflow-y-auto">
                  {lastEvent.type.includes('data_update') ? (
                    <div>
                      <div>ID: {lastEvent.data.candidateId || lastEvent.data.jobId || lastEvent.data.profileId}</div>
                      <div>Is New: {lastEvent.data.isNew ? 'Yes' : 'No'}</div>
                      <div>Updated By: {lastEvent.data.updatedBy}</div>
                    </div>
                  ) : (
                    <div>
                      <div>ID: {lastEvent.data.candidateId || lastEvent.data.jobId || lastEvent.data.profileId}</div>
                      <div>Status: {lastEvent.data.status}</div>
                      {lastEvent.data.comment && <div>Comment: {lastEvent.data.comment}</div>}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SSEStatusIndicator;
