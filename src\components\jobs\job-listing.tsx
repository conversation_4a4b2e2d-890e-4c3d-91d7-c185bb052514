import { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { toast } from "react-toastify";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Search,
  FileText,
  UserPlus,
  Edit,
  Trash2,

  X,
  Check,
} from "lucide-react";
import {
  createKebabMenuItem,
  type KebabMenuItem,
} from "@/components/ui/kebab-menu";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { useDebounce } from "@/hooks/use-debounce";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { useUser } from "@/contexts/user-context";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { ApiService, } from "@/services/api";
import { EditJobModal } from "@/components/modals/edit-job-modal";
import { JobDeletionModal } from "@/components/modals/job-deletion-modal";
import { fetchCandidates } from "@/store/slices/candidatesSlice";
import {
  fetchJobs,
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  type Job,
  type FilterTag,
} from "@/store/slices/jobsSlice";
import {
  selectJobsLoading,
  selectJobsError,
  selectSearchTags,
  selectCurrentPage,
  selectItemsPerPage,
  selectSortConfig,
  selectFilteredJobs,
  selectPaginatedJobs,
  selectSearchSuggestions,
  columns,
} from "@/store/selectors/jobsSelectors";
import { usePopupManager } from "@/hooks/use-popup-manager";
import { PopupManagerProvider } from "@/components/providers/popup-manager-provider";
// import { selectAvailableCandidates, selectCandidates } from "@/store/selectors/candidatesSelectors";
import { selectCandidates } from "@/store/selectors/candidatesSelectors";
import { Candidate } from "@/types/candidate";


const CustomKebabMenu = ({ items, popupId }: { items: KebabMenuItem[]; popupId: string }) => {
  const { openPopupId, setOpenPopupId } = usePopupManager();
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0, right: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const submenuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const isOpen = openPopupId === popupId;

  const updateMenuPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      const menuWidth = 180; // min-w-[180px]
      const menuHeight = 200; // Approximate height for positioning

      let left = 0;
      let top = rect.height + 4;
      if (rect.left + menuWidth > viewportWidth) {
        left = -menuWidth + rect.width;
      } else {
        left = 0;
      }

      if (rect.bottom + menuHeight > viewportHeight) {
        top = -menuHeight - 4;
      }

      setMenuPosition({ top, left, right: 0 });
    }
  };
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        submenuRef.current &&
        !submenuRef.current.contains(event.target as Node)
      ) {
        setOpenPopupId(null);
        setActiveSubmenu(null);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen, setOpenPopupId]);

  useEffect(() => {
    const handleResize = () => {
      if (isOpen) {
        updateMenuPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isOpen]);

  const handleMenuItemClick = (item: any) => {
    if (item.hasSubmenu) {
      setActiveSubmenu(activeSubmenu === item.id ? null : item.id);
    } else {
      item.onClick();
      setOpenPopupId(null);
      setActiveSubmenu(null);
    }
  };

  const handleToggleMenu = () => {
    if (!isOpen) {
      updateMenuPosition();
      setOpenPopupId(popupId);
    } else {
      setOpenPopupId(null);
    }
    setActiveSubmenu(null);
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleToggleMenu}
        className="p-1 rounded-full hover:bg-gray-100 focus:outline-none"
      >
        <svg
          className="w-4 h-4 text-gray-500"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
        </svg>
      </button>

      {isOpen && createPortal(
        <div
          className="fixed z-[99999] bg-white rounded-lg shadow-2xl border border-gray-200 min-w-[180px] py-1 animate-in fade-in-0 zoom-in-95 duration-200"
          style={{
            top: buttonRef.current ? buttonRef.current.getBoundingClientRect().top + menuPosition.top : 0,
            left: buttonRef.current ? buttonRef.current.getBoundingClientRect().left + menuPosition.left : 0,
            right: menuPosition.right === 0 ? undefined : 'auto'
          }}
        >
          {items.map((item) => (
            <div key={item.id} className="relative">
              <button
                onClick={() => handleMenuItemClick(item)}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors duration-150 flex items-center gap-2 ${item.variant === "destructive"
                  ? "text-red-600 hover:bg-red-50"
                  : "text-gray-700"
                  } ${item.separator ? "border-b border-gray-200" : ""}`}
              >
                {item.icon && <item.icon className="h-4 w-4 text-blue-500" />}
                <span className="flex-1">{item.label}</span>
                {item.hasSubmenu && (
                  <svg
                    className={`w-4 h-4 transition-transform ${activeSubmenu === item.id ? "rotate-90" : ""
                      }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                )}
              </button>

              {/* Submenu */}
              {item.hasSubmenu && activeSubmenu === item.id && (
                <div
                  ref={submenuRef}
                  className="absolute right-full top-0 mr-1 z-[100000] bg-white rounded-lg shadow-2xl border border-gray-200 min-w-[180px] py-1 animate-in fade-in-0 slide-in-from-left-2 duration-200"
                >
                  {item.submenuItems?.map((subItem: any) => (
                    <button
                      key={subItem.id}
                      onClick={() => {
                        subItem.action();
                        setOpenPopupId(null);
                        setActiveSubmenu(null);
                      }}
                      className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors duration-150 flex items-center gap-2 text-gray-700"
                    >
                      {subItem.icon && <subItem.icon className="h-4 w-4 text-blue-500" />}
                      <span>{subItem.label}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>,
        document.body
      )}
    </div>
  );
};

// --- PARENT COMPONENT ---
export function JobListing() {
  return (
    <PopupManagerProvider>
      <div className="h-full w-full flex flex-col">
        <div className="bg-white p-6 flex-1 w-full">
          <JobListingTable />
        </div>
      </div>
    </PopupManagerProvider>
  );
}

// --- MAIN TABLE COMPONENT ---
function JobListingTable() {
  const { userEmail, userName, userId, userRole } = useUser();
  const dispatch = useAppDispatch();
  console.log('Component dispatch function:', dispatch);
  const navigate = useNavigate();
  const location = useLocation();

  // Redux state
  const loading = useAppSelector(selectJobsLoading);
  const error = useAppSelector(selectJobsError);
  const searchTags = useAppSelector(selectSearchTags);

  // Edit modal state
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedJobForEdit, setSelectedJobForEdit] = useState<Job | null>(null);

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedJobForDelete, setSelectedJobForDelete] = useState<Job | null>(null);


  const currentPage = useAppSelector(selectCurrentPage);
  const itemsPerPage = useAppSelector(selectItemsPerPage);
  console.log('Component received itemsPerPage:', itemsPerPage);
  const sortConfig = useAppSelector(selectSortConfig);
  const filteredJobs = useAppSelector(selectFilteredJobs);
  const currentJobs = useAppSelector(selectPaginatedJobs);

  // Debug logging for pagination state
  useEffect(() => {
    console.log('Pagination state:', {
      currentPage,
      itemsPerPage,
      totalJobs: filteredJobs.length,
      currentJobsCount: currentJobs.length,
      totalPages: Math.ceil(filteredJobs.length / itemsPerPage)
    });
  }, [currentPage, itemsPerPage, filteredJobs.length, currentJobs.length]);

  // Debug logging for Redux state changes
  useEffect(() => {
    console.log('Redux state changed:', {
      itemsPerPage,
      currentPage,
      filteredJobsLength: filteredJobs.length
    });
  }, [itemsPerPage, currentPage, filteredJobs.length]);

  // Debug logging for component mount and initial state
  useEffect(() => {
    console.log('JobListing component mounted with initial state:', {
      itemsPerPage,
      currentPage,
      filteredJobsLength: filteredJobs.length,
      currentJobsCount: currentJobs.length
    });
  }, []);

  // Local UI state
  const [inputValue, setInputValue] = useState("");
  const debouncedInputValue = useDebounce(inputValue, 250);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);


  // Refs
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const dateButtonRef = useRef<HTMLButtonElement>(null);

  // Available candidates modal state
  // const [isTalentPoolModalOpen, setIsTalentPoolModalOpen] = useState(false);
  // const [selectedJobForCandidate, setSelectedJobForCandidate] = useState<Job | null>(null);
  // const [candidateSearchTerm, setCandidateSearchTerm] = useState("");

  // Job selection state for bulk assignment
  // const [selectedJobs, setSelectedJobs] = useState<Set<number>>(new Set());

  // Get search suggestions from Redux
  const suggestions = useAppSelector((state) =>
    selectSearchSuggestions(state, debouncedInputValue)
  );

  // Get available candidates for assignment (only unassigned candidates)
  // const candidates = useAppSelector(selectAvailableCandidates);

  // Get all candidates to check for job associations
  const allCandidates = useAppSelector(selectCandidates);

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Fetch jobs and candidates on component mount
  useEffect(() => {
    const username = userName || userEmail || "managerone";
    dispatch(fetchJobs({ username }));

    // Also fetch candidates for the assignment modal
    if (userId && userRole && userName) {
      const userType = userRole === "manager" ? "management" : "recruiter";
      dispatch(fetchCandidates({ userId, userType, userName }));
    }
  }, [dispatch, userName, userEmail, userId, userRole]);

  // useEffects for UI interactions (closing dropdowns)
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setInputValue(""); // Clear input instead of suggestions since suggestions come from Redux
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [searchContainerRef]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dateDropdownRef.current &&
        !dateDropdownRef.current.contains(event.target as Node) &&
        dateButtonRef.current &&
        !dateButtonRef.current.contains(event.target as Node)
      ) {
        setIsDateDropdownOpen(false);
      }
    }
    if (isDateDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDateDropdownOpen]);

  // --- HANDLER FUNCTIONS ---
  const handleAddTag = (
    tagOrSuggestion: string | { value: string; column: string }
  ) => {
    const newTag: FilterTag =
      typeof tagOrSuggestion === "string"
        ? { value: tagOrSuggestion.trim(), column: "Any" }
        : {
          value: (tagOrSuggestion.value || "").trim(),
          column: tagOrSuggestion.column,
        };
    if (
      newTag.value &&
      !searchTags.some(
        (t) => t.value === newTag.value && t.column === newTag.column
      )
    ) {
      dispatch(addSearchTag(newTag));
    }
    setInputValue("");
  };
  const handleRemoveTag = (tagToRemove: FilterTag) => {
    dispatch(removeSearchTag(tagToRemove));
  };
  const handleApplyFilters = () => {
    dispatch(applyFilters());
  };
  const handleClearAllFilters = () => {
    dispatch(clearAllFilters());
    setInputValue("");

  };
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };


  const paginate = async (pageNumber: number) => {
    await animatePagination();
    dispatch(setCurrentPage(pageNumber));
  };
  const handleSort = async (key: keyof Job | "posted_by") => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }
    dispatch(setSortConfig({ key, direction }));
  };
  const getStatusColor = (status: Job["job_status"]) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Closed":
        return "bg-red-100 text-red-800";
      case "On Hold":
        return "bg-yellow-100 text-yellow-800";
      case "Hold":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleViewJD = async (job: Job) => {
    try {
      await ApiService.downloadJobDescription(job.id);
    } catch (error) {
      // Error downloading job description
      toast.error("Failed to download job description");
    }
  };

  // Handler for editing job
  const handleEditJob = (job: Job) => {
    setSelectedJobForEdit(job);
    setIsEditModalOpen(true);
  };

  // Handler for deleting job
  const handleDeleteJob = (job: Job) => {
    setSelectedJobForDelete(job);
    setIsDeleteModalOpen(true);
  };



  // Helper function to get candidates associated with a job
  const getAssociatedCandidates = (job: Job): Candidate[] => {
    return allCandidates.filter(candidate => {
      try {
        if (!candidate || !candidate.jobId) return false;

        // Try exact match first
        if (candidate.jobId === job.id.toString()) return true;

        // Try string comparison
        if (String(candidate.jobId) === String(job.id)) return true;

        return false;
      } catch (error) {
        console.error('Error filtering candidate:', error, candidate);
        return false;
      }
    });
  };

  // Handler for assigning candidate from available candidates
  // const handleAssignCandidate = async (candidate: any) => {
  //   const firstJobId = Array.from(selectedJobs)[0];
  //   const firstJob = currentJobs.find(job => job.id === firstJobId);
  //   if (firstJob && candidate) {
  //     try {
  //       // Prepare check_candidate payload
  //       const checkPayload = {
  //         name: `${candidate.firstName} ${candidate.lastName}`,
  //         mobile: candidate.phone,
  //         email: candidate.email,
  //         total_experience_years: String(Math.floor(candidate.experience)),
  //         total_experience_months: String(Math.round((candidate.experience % 1) * 12)),
  //         relevant_experience_years: String(Math.floor(candidate.experience)),
  //         relevant_experience_months: String(Math.round((candidate.experience % 1) * 12)),
  //         qualifications: candidate.education,
  //         last_working_date: '', // Not available in Candidate type
  //         serving_notice_period: '', // Not available in Candidate type
  //       };
  //       const checkRes = await ApiService.checkCandidate(checkPayload);
  //       if (
  //         checkRes &&
  //         checkRes.jobIds &&
  //         checkRes.clients &&
  //         checkRes.profiles &&
  //         checkRes.jobIds.includes(firstJob.id) &&
  //         checkRes.clients.includes(firstJob.client) &&
  //         checkRes.profiles.includes(firstJob.role)
  //       ) {
  //         toast.info('Candidate already assigned to this job/client/profile.');
  //         return;
  //       }
  //       const payload = {
  //         id: candidate.id,
  //         job_id: firstJob.id,
  //         client: firstJob.client,
  //         profile: firstJob.role,
  //       };
  //       const res = await ApiService.assignClientToJob(payload);
  //       if (res.status === 'success') {
  //         toast.success('Candidate assigned successfully!');
  //       } else {
  //         toast.error(res.message || 'Assignment failed.');
  //       }
  //     } catch (error) {
  //       toast.error('Error assigning candidate: ' + (error instanceof Error ? error.message : 'Unknown error'));
  //     }
  //   }
  //   setIsTalentPoolModalOpen(false);
  //   setSelectedJobForCandidate(null);
  //   setSelectedJobs(new Set()); // Clear selection after assignment
  // };

  // Close available candidates modal
  // const closeTalentPoolModal = () => {
  //   setIsTalentPoolModalOpen(false);
  //   setSelectedJobForCandidate(null);
  // };



  // Handle job selection for bulk assignment
  // const handleJobSelection = (jobId: number, checked: boolean) => {
  //   const newSelectedJobs = new Set(selectedJobs);
  //   if (checked) {
  //     newSelectedJobs.add(jobId);
  //   } else {
  //     newSelectedJobs.delete(jobId);
  //   }
  //   setSelectedJobs(newSelectedJobs);
  // };

  // Handle select all jobs
  // const handleSelectAllJobs = (checked: boolean) => {
  //   if (checked) {
  //     const allJobIds = currentJobs.map(job => job.id);
  //     setSelectedJobs(new Set(allJobIds));
  //   } else {
  //     setSelectedJobs(new Set());
  //   }
  // };

  // Handle bulk submit candidate
  // const handleBulkSubmitCandidate = () => {
  //   if (selectedJobs.size === 0) {
  //     toast.error("Please select at least one job");
  //     return;
  //   }
  //   // For now, we'll use the first selected job for the modal
  //   // In the future, we could show multiple jobs in the modal
  //   const firstJobId = Array.from(selectedJobs)[0];
  //   const firstJob = currentJobs.find(job => job.id === firstJobId);
  //   if (firstJob) {
  //     setSelectedJobForCandidate(firstJob);
  //     setIsTalentPoolModalOpen(true);
  //   }
  // };


  const createJobMenuItems = (job: Job): KebabMenuItem[] => [
    createKebabMenuItem(
      "add-candidate",
      "Add Candidate",
      () => {
        // Navigate to register candidate with job details
        const currentPath = location.pathname;
        const basePath = currentPath.includes("/manager/")
          ? "/manager"
          : "/recruiter";
        navigate(`${basePath}/register-candidate/${job.id}`, {
          state: {
            jobId: job.id,
            client: job.client,
            role: job.role,
            jobStatus: job.job_status,
          },
        });
      },
      { icon: UserPlus }
    ),
    createKebabMenuItem(
      "view-jd",
      "View Job Description",
      () => handleViewJD(job),
      { icon: FileText, separator: true }
    ),
    createKebabMenuItem(
      "edit-job",
      "Edit Job",
      () => handleEditJob(job),
      { icon: Edit }
    ),
    createKebabMenuItem(
      "delete-job",
      "Delete Job",
      () => handleDeleteJob(job),
      { icon: Trash2, variant: "destructive" }
    ),
  ];

  return (
    <div className="flex flex-col">
      {error && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-800">
          <strong>API Connection Issue:</strong> {error}. Showing stale or empty
          data.
        </div>
      )}
      <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
        <div className="flex-1 min-w-[300px] relative" ref={searchContainerRef}>
          <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500">
            <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
            {searchTags.map((tag) => (
              <span
                key={`${tag.column}-${tag.value}`}
                className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                <span className="font-normal text-blue-600 mr-1">
                  {tag.column}:
                </span>
                {tag.value}
                <button
                  onClick={() => handleRemoveTag(tag)}
                  className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </span>
            ))}
            <input
              type="text"
              className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none"
              placeholder="Search and add filters..."
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={(e) => {
                if (e.key === "Enter" && inputValue.trim()) {
                  e.preventDefault();
                  handleAddTag(inputValue);
                }
              }}
            />
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
            <button
              onClick={handleApplyFilters}
              title="Apply Filters"
              className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600"
            >
              <Check className="h-5 w-5" />
            </button>
            <button
              onClick={handleClearAllFilters}
              title="Clear All Filters"
              className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          {suggestions.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div
                  key={`${s.value}-${i}`}
                  className="cursor-pointer p-3 hover:bg-blue-50 flex justify-between items-center text-sm"
                  onClick={() => handleAddTag(s)}
                >
                  <span className="font-medium text-gray-900">{s.value}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md font-medium">
                    {s.column}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
        <div className="flex flex-wrap items-center gap-3">
          {/* Submit Candidate Button */}
          {/* <button
            onClick={handleBulkSubmitCandidate}
            disabled={selectedJobs.size === 0}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 transition-colors"
            title={selectedJobs.size === 0 ? "Select jobs first" : `Submit candidate to ${selectedJobs.size} selected job(s)`}
          >
            <UserPlus className="h-4 w-4" />
            Submit Candidate
            {selectedJobs.size > 0 && (
              <span className="bg-white text-green-600 px-2 py-0.5 rounded-full text-xs font-bold">
                {selectedJobs.size}
              </span>
            )}
          </button> */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500 min-w-[120px]"
            onChange={async (e) => {
              try {
                const newItemsPerPage = Number(e.target.value);
                console.log('Changing items per page from', itemsPerPage, 'to', newItemsPerPage);

                if (isNaN(newItemsPerPage) || newItemsPerPage <= 0) {
                  console.error('Invalid items per page value:', e.target.value);
                  return;
                }

                await animatePagination();
                console.log('Dispatching setItemsPerPage with:', newItemsPerPage);
                dispatch(setItemsPerPage(newItemsPerPage));
                console.log('Dispatching setCurrentPage with: 1');
                // Reset to first page when changing items per page
                dispatch(setCurrentPage(1));

                console.log('Successfully updated items per page to:', newItemsPerPage);
              } catch (error) {
                console.error('Error updating items per page:', error);
              }
            }}
            value={itemsPerPage}
          >
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
          </select>
        </div>
      </div>
      <AnimatedTableWrapper
        isLoading={isLoading || loading}
        loadingComponent={<TableSkeleton rows={8} cols={columns.length + 2} />}
        className="border border-gray-200 rounded-md overflow-visible flex-1"
      >
        <div className="overflow-auto max-h-[67vh] sm:h-[500px] relative table-scrollbar">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {/* Select All Checkbox - Commented out */}
                {/* <th
                  scope="col"
                  className="px-2 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200"
                >
                  <input
                    type="checkbox"
                    checked={selectedJobs.size === currentJobs.length && currentJobs.length > 0}
                    onChange={(e) => handleSelectAllJobs(e.target.checked)}
                    className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                  />
                </th> */}
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className="px-2 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                <th
                  scope="col"
                  className="px-2 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td
                    colSpan={columns.length + 2}
                    className="text-center py-4 text-xs text-gray-500"
                  >
                    Loading jobs...
                  </td>
                </tr>
              ) : currentJobs.length > 0 ? (
                currentJobs.map((job, index) => (
                  <AnimatedTableRow key={job.id} index={index}>
                    {/* Job Selection Checkbox - Commented out */}
                    {/* <td className="px-2 py-2 text-center">
                      <input
                        type="checkbox"
                        checked={selectedJobs.has(job.id)}
                        onChange={(e) => handleJobSelection(job.id, e.target.checked)}
                        className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                      />
                    </td> */}
                    {/* FIX: No whitespace here */}
                    {columns.map((column) => {
                      const cellClassName =
                        "px-2 py-2 text-xs text-gray-800 font-medium";
                      if (column.key === "job_status") {
                        return (
                          <td
                            key={`${job.id}-status`}
                            className={cellClassName}
                          >
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${getStatusColor(
                                job.job_status
                              )}`}
                            >
                              {job.job_status}
                            </span>
                          </td>
                        );
                      }
                      if (column.key === "posted_by") {
                        return (
                          <td
                            key={`${job.id}-postedby`}
                            className={cellClassName}
                          >
                            <span
                              className="font-semibold text-blue-600 truncate block whitespace-nowrap overflow-hidden"
                              title={job.management}
                            >
                              {job.management}
                            </span>
                          </td>
                        );
                      }
                      if (column.key === "client") {
                        return (
                          <td
                            key={`${job.id}-client`}
                            className={cellClassName}
                          >
                            <span
                              className="font-semibold text-green-700 truncate block whitespace-nowrap overflow-hidden"
                              title={job.client}
                            >
                              {job.client}
                            </span>
                          </td>
                        );
                      }
                      if (column.key === "recruiter") {
                        return (
                          <td
                            key={`${job.id}-recruiter`}
                            className={`${cellClassName} max-w-xs`}
                          >
                            <span
                              className="block truncate"
                              title={job.recruiter}
                            >
                              {job.recruiter}
                            </span>
                          </td>
                        );
                      }
                      return (
                        <td
                          key={`${job.id}-${String(column.key)}`}
                          className={`${cellClassName} whitespace-nowrap `}
                        >
                          {String(job[column.key as keyof Job] || "")}
                        </td>
                      );
                    })}
                    <td className="px-2 py-2 text-xs text-center">
                      <CustomKebabMenu items={createJobMenuItems(job)} popupId={`job-${job.id}`} />
                    </td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length + 2}
                    className="text-center py-4 text-xs text-gray-500"
                  >
                    No jobs found matching your criteria.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mt-2 text-sm text-gray-700 gap-4 bg-white p-2 rounded-md border border-gray-200">
        <div className="text-xs sm:text-sm">
          {filteredJobs.length > 0 ? (
            <>
              Showing{" "}
              {Math.min(
                1 + (currentPage - 1) * itemsPerPage,
                filteredJobs.length
              )}{" "}
              to {Math.min(currentPage * itemsPerPage, filteredJobs.length)} of{" "}
              {filteredJobs.length} jobs
            </>
          ) : (
            "No jobs found"
          )}
        </div>
        <div className="w-full sm:w-auto flex justify-center sm:justify-end">
          <AnimatedPagination
            currentPage={currentPage}
            totalPages={Math.ceil(filteredJobs.length / itemsPerPage)}
            onPageChange={paginate}
          />
        </div>
      </div>

      {/* Available Candidates Modal - Slides from right to center */}
      {/* {isTalentPoolModalOpen && selectedJobForCandidate && (
        <div className="fixed inset-0 z-50 flex justify-end">
          <div className="h-full w-150 bg-white shadow-2xl transform transition-transform duration-300 ease-in-out">
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    Select from Available Candidates
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {selectedJobForCandidate.client} - {selectedJobForCandidate.role}
                    {selectedJobs.size > 1 && (
                      <span className="ml-2 text-blue-600 font-medium">
                        (+{selectedJobs.size - 1} more)
                      </span>
                    )}
                  </p>
                </div>
                <button
                  onClick={closeTalentPoolModal}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Candidate Name, Email, Con"
                    value={candidateSearchTerm}
                    onChange={(e) => setCandidateSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex-1 overflow-y-auto">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h4 className="text-xl font-bold text-gray-900">Available Candidates</h4>
                      <p className="text-gray-600 mt-1">Choose a candidate to assign to the selected job(s)</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">
                        {(() => {
                          const filteredCandidates = candidates.filter(candidate => {
                            const fullName = candidate.name.toLowerCase();
                            return fullName.includes(candidateSearchTerm.toLowerCase()) ||
                              candidate.email?.toLowerCase().includes(candidateSearchTerm.toLowerCase()) ||
                              candidate.skills?.toLowerCase().includes(candidateSearchTerm.toLowerCase());
                          });
                          return filteredCandidates.length;
                        })()}
                      </div>
                      <div className="text-sm text-gray-500">Available Candidates</div>
                    </div>
                  </div>

                  {(() => {
                    const filteredCandidates = candidates.filter(candidate => {
                      const fullName = candidate.name.toLowerCase();
                      return fullName.includes(candidateSearchTerm.toLowerCase()) ||
                        candidate.email?.toLowerCase().includes(candidateSearchTerm.toLowerCase()) ||
                        candidate.skills?.toLowerCase().includes(candidateSearchTerm.toLowerCase());
                    });

                    if (filteredCandidates.length === 0) {
                      return (
                        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                            <div className="grid grid-cols-4 gap-4 text-sm font-medium text-gray-700">
                              <div>Name</div>
                              <div>Email</div>
                              <div>Skills</div>
                              <div>Experience</div>
                            </div>
                          </div>

                          <div className="text-center py-16">
                            <div className="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                              <UserPlus className="w-8 h-8 text-gray-400" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                              {candidateSearchTerm ? 'No matches found' : 'No available candidates'}
                            </h3>
                            <p className="text-gray-500 mb-4 text-sm">
                              {candidateSearchTerm
                                ? 'Try different search terms or check your filters'
                                : 'No unassigned candidates are currently available for assignment'
                              }
                            </p>
                          </div>
                        </div>
                      );
                    }

                    return (
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                          <div className="grid grid-cols-5 gap-4 text-sm font-medium text-gray-700">
                            <div>#</div>
                            <div>Name</div>
                            <div>Email</div>
                            <div>Skills</div>
                            <div>Actions</div>
                          </div>
                        </div>

                        <div className="divide-y divide-gray-200">
                          {filteredCandidates.map((candidate, index) => (
                            <div
                              key={candidate.id}
                              className="group px-4 py-3 hover:bg-gray-50 transition-colors cursor-pointer"
                              onClick={() => handleAssignCandidate(candidate)}
                            >
                              <div className="grid grid-cols-5 gap-4 items-center">
                                <div className="text-sm font-medium text-gray-600">
                                  {index + 1}
                                </div>

                                <div className="text-sm font-medium text-gray-900">
                                  {candidate.name}
                                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                    #{candidate.id}
                                  </span>
                                </div>

                                <div className="text-sm text-gray-600 truncate">
                                  {candidate.email}
                                </div>

                                <div className="text-sm text-gray-600 truncate">
                                  {candidate.skills || 'Not specified'}
                                </div>

                                <div className="flex-shrink-0">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAssignCandidate(candidate);
                                    }}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors"
                                  >
                                    Assign
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        </div>
      )} */}

      {/* Edit Job Modal */}
      <EditJobModal
        job={selectedJobForEdit}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedJobForEdit(null);
        }}
        onSave={() => {
          // Refresh the jobs list to get the updated data
          const username = userName || userEmail || "managerone";
          dispatch(fetchJobs({ username }));
          setIsEditModalOpen(false);
          setSelectedJobForEdit(null);
        }}
      />

      {/* Job Deletion Modal */}
      <JobDeletionModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedJobForDelete(null);
        }}
        job={selectedJobForDelete}
        associatedCandidates={selectedJobForDelete ? getAssociatedCandidates(selectedJobForDelete) : []}
        userName={userName}
        userEmail={userEmail}
      />
    </div>
  );
}
