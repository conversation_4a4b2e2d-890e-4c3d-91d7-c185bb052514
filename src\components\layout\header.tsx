import { useState } from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { SOPIcon } from "@/components/ui/sop-icon";
import { SOPModal } from "@/components/modals/sop-modal";
import ATS_LOGO from "@/assets/ATS_LOGO.png";

export function Header() {
  const [isSOPModalOpen, setIsSOPModalOpen] = useState(false);

  return (
    <>
      <header className="bg-white border-b border-gray-200 py-1 px-6 flex items-center justify-between">
        {/* Left Side - Makonis Logo */}
        <div className="flex items-center">
          <SidebarTrigger className="mr-4" />
          <img src={ATS_LOGO} alt="Makonis Logo" className="h-12 w-auto" />
        </div>

        {/* Center - Makonis Text */}
        <div className="flex-1 flex justify-center">
          <div className="text-center">
            <div className="text-xl font-bold text-blue-900">
              TalentTrack Pro
            </div>
          </div>
        </div>

        {/* Right Side - SOP Button */}
        <div className="flex items-center">
          <button
            onClick={() => setIsSOPModalOpen(true)}
            className="flex items-center gap-2 px-1 py-1 text-sm font-medium text-gray-700  hover:bg-gray-200 rounded-md transition-colors"
            title="View Standard Operating Procedures"
          >
            <SOPIcon className="h-4 w-4" />
            View SOP
          </button>
        </div>
      </header>

      {/* SOP Modal */}
      <SOPModal
        isOpen={isSOPModalOpen}
        onClose={() => setIsSOPModalOpen(false)}
      />
    </>
  );
}
