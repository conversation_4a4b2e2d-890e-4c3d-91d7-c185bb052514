# Enhanced SSE (Server-Sent Events) Integration Guide

## Overview

The enhanced SSE system now supports both **status updates** and **complete data updates** for candidates, jobs, and profiles. When a candidate ID matches existing data, it can either update just the status or replace the entire candidate object with new data.

## Key Features

### 1. Dual Update Types
- **Status Updates**: Update only status, comment, and timestamp
- **Data Updates**: Replace entire object with new data from SSE

### 2. New vs Existing Data Handling
- **Existing Data**: When `candidateId == data.id`, the system updates/replaces the existing record
- **New Data**: When `data.id` is new, it's added to the Redux store correctly

### 3. Enhanced Event Types
- `candidate_update` - Status only updates
- `candidate_data_update` - Complete candidate data updates
- `job_update` - Job status only updates  
- `job_data_update` - Complete job data updates
- `profile_update` - Profile status only updates
- `profile_data_update` - Complete profile data updates

## Usage Examples

### 1. Basic Integration with Hook

```typescript
import { useSSEIntegration } from '@/hooks/useSSEIntegration';

const MyComponent = () => {
  const { isConnected } = useSSEIntegration({
    userId: "123",
    userRole: "recruiter",
    showNotifications: true,
    onCandidateUpdate: (event) => {
      console.log('Candidate updated:', event);
    }
  });

  return <div>SSE Status: {isConnected() ? 'Connected' : 'Disconnected'}</div>;
};
```

### 2. Manual SSE Service Usage

```typescript
import { sseService } from '@/services/sse';

// Connect
sseService.connect(userId, userRole);

// Listen for events
sseService.addEventListener((event) => {
  if (event.type === 'candidate_data_update') {
    const { candidateData, isNew } = event.data;
    console.log('Complete candidate data:', candidateData);
    console.log('Is new candidate:', isNew);
  }
});

// Disconnect when done
sseService.disconnect();
```

### 3. Redux Integration

The enhanced Redux actions automatically handle the data updates:

```typescript
// For status updates
dispatch(updateCandidateStatus({ 
  id: candidateId, 
  status: 'Approved', 
  comment: 'Good candidate' 
}));

// For complete data updates
dispatch(updateCandidateData({ 
  candidateData: completeCandidate, 
  isNew: false 
}));
```

## SSE Event Data Structure

### Status Update Events
```typescript
{
  type: 'candidate_update',
  data: {
    candidateId: 123,
    status: 'Approved',
    comment: 'Good candidate',
    updatedBy: '<EMAIL>',
    updatedAt: '2024-09-04T10:30:00Z'
  },
  timestamp: '2024-09-04T10:30:00Z'
}
```

### Complete Data Update Events
```typescript
{
  type: 'candidate_data_update',
  data: {
    candidateId: 123,
    candidateData: {
      id: 123,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      status: 'Approved',
      // ... complete candidate object
    },
    isNew: false,
    updatedBy: '<EMAIL>',
    updatedAt: '2024-09-04T10:30:00Z'
  },
  timestamp: '2024-09-04T10:30:00Z'
}
```

## Server-Side Implementation

To send enhanced SSE events from your server:

### Status Update
```javascript
// Send status-only update
const statusEvent = {
  event_type: 'candidate_updated',
  id: 123,
  status: 'Approved',
  comment: 'Good candidate',
  recruiter: '<EMAIL>'
};
```

### Complete Data Update
```javascript
// Send complete data update
const dataEvent = {
  event_type: 'candidate_updated',
  id: 123,
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  client: 'ABC Corp',
  profile: 'Software Engineer',
  skills: 'React, Node.js',
  status: 'Approved',
  recruiter: '<EMAIL>',
  // ... include all candidate fields
};
```

## Data Detection Logic

The system automatically detects whether an SSE event contains complete data:

### For Candidates
Checks for presence of: `name`, `email`, `phone`

### For Jobs  
Checks for presence of: `role`, `client`, `skills`

### For Profiles
Uses the same logic as candidates

## Components

### SSEStatusIndicator
A visual component to monitor SSE connection and events:

```typescript
import SSEStatusIndicator from '@/components/sse-status-indicator';

<SSEStatusIndicator 
  userId="123" 
  userRole="recruiter" 
  className="fixed bottom-4 right-4"
/>
```

## Best Practices

1. **Always include complete data** when possible in SSE events
2. **Use the hook** for component integration rather than direct service calls
3. **Handle both event types** in your components for maximum compatibility
4. **Test with both new and existing data** scenarios
5. **Monitor the SSE connection status** in production

## Troubleshooting

### Events Not Updating Redux
- Check if the candidate/job/profile ID exists in the current state
- Verify the SSE event contains the expected data structure
- Check browser console for SSE connection errors

### New Data Not Appearing
- Ensure `isNew: true` flag is set correctly
- Verify the data structure matches your Redux state interface
- Check if filters are hiding the new data

### Connection Issues
- Verify the SSE endpoint URL is correct
- Check network connectivity
- Monitor browser developer tools for SSE connection status
