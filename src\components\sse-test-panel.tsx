import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { updateCandidateData, updateCandidateStatus } from '@/store/slices/candidatesSlice';
import { updateJobData, updateJobStatus } from '@/store/slices/jobsSlice';
import { updateProfileData, updateProfileStatus } from '@/store/slices/peerAssignedProfilesSlice';
import { sseService } from '@/services/sse';

/**
 * Test panel component to simulate SSE events for testing purposes
 * This component allows you to manually trigger different types of SSE events
 */
export const SSETestPanel: React.FC = () => {
  const dispatch = useDispatch();
  const [candidateId, setCandidateId] = useState('123');
  const [jobId, setJobId] = useState('456');
  const [profileId, setProfileId] = useState('789');

  // Simulate candidate status update
  const simulateCandidateStatusUpdate = () => {
    dispatch(updateCandidateStatus({
      id: parseInt(candidateId),
      status: 'Approved',
      comment: 'Test status update via SSE simulation'
    }));
    console.log('Simulated candidate status update for ID:', candidateId);
  };

  // Simulate complete candidate data update
  const simulateCandidateDataUpdate = () => {
    const mockCandidateData = {
      id: parseInt(candidateId),
      name: 'John Doe (SSE Updated)',
      email: '<EMAIL>',
      phone: '+1234567890',
      client: 'ABC Corporation',
      profile: 'Senior Software Engineer',
      skills: 'React, TypeScript, Node.js, SSE Integration',
      status: 'Approved',
      recruiter: 'Test Recruiter',
      comment: 'Complete data updated via SSE simulation',
      jobId: 'JOB-001',
      appliedDate: new Date().toISOString(),
      lastUpdated: new Date().toISOString().split('T')[0],
      experience: 5,
      education: 'Bachelor of Computer Science',
      location: 'Remote',
      salary: '80000 - 120000',
      notes: 'Updated via SSE test panel',
      source: 'SSE Test',
      peerReviewer: '',
      management: null,
      clientAssigned: false,
    };

    dispatch(updateCandidateData({
      candidateData: mockCandidateData,
      isNew: false // Set to true to test new candidate addition
    }));
    console.log('Simulated candidate data update for ID:', candidateId, mockCandidateData);
  };

  // Simulate new candidate addition
  const simulateNewCandidate = () => {
    const newCandidateId = Date.now(); // Use timestamp as unique ID
    const mockNewCandidate = {
      id: newCandidateId,
      name: 'Jane Smith (New via SSE)',
      email: '<EMAIL>',
      phone: '+0987654321',
      client: 'XYZ Company',
      profile: 'Frontend Developer',
      skills: 'Vue.js, CSS, HTML, JavaScript',
      status: 'Applied',
      recruiter: 'Test Recruiter',
      comment: 'New candidate added via SSE simulation',
      jobId: 'JOB-002',
      appliedDate: new Date().toISOString(),
      lastUpdated: new Date().toISOString().split('T')[0],
      experience: 3,
      education: 'Bachelor of Arts',
      location: 'New York',
      salary: '60000 - 90000',
      notes: 'Added via SSE test panel',
      source: 'SSE Test',
      peerReviewer: '',
      management: null,
      clientAssigned: false,
    };

    dispatch(updateCandidateData({
      candidateData: mockNewCandidate,
      isNew: true
    }));
    console.log('Simulated new candidate addition:', newCandidateId, mockNewCandidate);
  };

  // Simulate job status update
  const simulateJobStatusUpdate = () => {
    dispatch(updateJobStatus({
      id: parseInt(jobId),
      status: 'Closed'
    }));
    console.log('Simulated job status update for ID:', jobId);
  };

  // Simulate complete job data update
  const simulateJobDataUpdate = () => {
    const mockJobData = {
      id: parseInt(jobId),
      role: 'Full Stack Developer (SSE Updated)',
      client: 'Tech Innovations Inc',
      skills: 'React, Node.js, PostgreSQL, AWS',
      recruiter: 'Test Recruiter',
      job_status: 'Active',
      location: 'San Francisco, CA',
      experience_min: '3',
      experience_max: '7',
      budget_min: '90000',
      budget_max: '140000',
      no_of_positions: '2',
      job_type: 'Full Time',
      mode: 'Hybrid',
      country: 'USA',
      management: 'Engineering Team',
      detailed_jd: 'Updated job description via SSE simulation...',
      date_created: new Date().toISOString(),
      time_created: new Date().toLocaleTimeString(),
      data_updated_date: new Date().toISOString().split('T')[0],
      data_updated_time: new Date().toLocaleTimeString(),
      notice_period: '2 weeks',
      shift_timings: '9 AM - 6 PM PST',
      contract_in_months: null,
      custom_job_type: null,
      jd_pdf_present: false,
      jd_pdf_extension: null,
    };

    dispatch(updateJobData({
      jobData: mockJobData,
      isNew: false
    }));
    console.log('Simulated job data update for ID:', jobId, mockJobData);
  };

  // Check SSE connection status
  const checkSSEStatus = () => {
    const isConnected = sseService.getConnectionStatus();
    alert(`SSE Connection Status: ${isConnected ? 'Connected' : 'Disconnected'}`);
  };

  return (
    <div className="p-6 bg-gray-50 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">SSE Test Panel</h3>
      <p className="text-sm text-gray-600 mb-4">
        Use this panel to simulate SSE events and test the enhanced data update functionality.
      </p>

      {/* Connection Status */}
      <div className="mb-6">
        <button
          onClick={checkSSEStatus}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Check SSE Connection Status
        </button>
      </div>

      {/* Candidate Tests */}
      <div className="mb-6">
        <h4 className="font-medium mb-2">Candidate Tests</h4>
        <div className="flex gap-2 mb-2">
          <input
            type="text"
            value={candidateId}
            onChange={(e) => setCandidateId(e.target.value)}
            placeholder="Candidate ID"
            className="px-3 py-1 border rounded text-sm"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={simulateCandidateStatusUpdate}
            className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
          >
            Status Update
          </button>
          <button
            onClick={simulateCandidateDataUpdate}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          >
            Data Update
          </button>
          <button
            onClick={simulateNewCandidate}
            className="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
          >
            Add New Candidate
          </button>
        </div>
      </div>

      {/* Job Tests */}
      <div className="mb-6">
        <h4 className="font-medium mb-2">Job Tests</h4>
        <div className="flex gap-2 mb-2">
          <input
            type="text"
            value={jobId}
            onChange={(e) => setJobId(e.target.value)}
            placeholder="Job ID"
            className="px-3 py-1 border rounded text-sm"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={simulateJobStatusUpdate}
            className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
          >
            Status Update
          </button>
          <button
            onClick={simulateJobDataUpdate}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          >
            Data Update
          </button>
        </div>
      </div>

      <div className="text-xs text-gray-500">
        Check the browser console for detailed logs of the simulated events.
        These actions directly update Redux state to simulate SSE behavior.
      </div>
    </div>
  );
};

export default SSETestPanel;
