import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { Candidate, Column } from '@/types/candidate';
import { FilterTag } from '../slices/candidatesSlice';

// Base selectors
export const selectCandidates = (state: RootState) => state.candidates.candidates;
export const selectCandidatesLoading = (state: RootState) => state.candidates.loading;
export const selectCandidatesError = (state: RootState) => state.candidates.error;
export const selectLastFetched = (state: RootState) => state.candidates.lastFetched;
export const selectApiResponse = (state: RootState) => state.candidates.apiResponse;
export const selectSearchTags = (state: RootState) => state.candidates.searchTags;
export const selectAppliedTags = (state: RootState) => state.candidates.appliedTags;
export const selectDateFilter = (state: RootState) => state.candidates.dateFilter;
export const selectAppliedCustomDateRange = (state: RootState) => state.candidates.appliedCustomDateRange;
export const selectCurrentPage = (state: RootState) => state.candidates.currentPage;
export const selectItemsPerPage = (state: RootState) => state.candidates.itemsPerPage;
export const selectSortConfig = (state: RootState) => state.candidates.sortConfig;
export const selectSelectedRows = (state: RootState) => state.candidates.selectedRows;
export const selectSelectAll = (state: RootState) => state.candidates.selectAll;
export const selectVisibleColumns = (state: RootState) => state.candidates.visibleColumns;

// Column definitions
export const candidateColumns: Column[] = [
  { key: "appliedDate", label: "Date", sortable: true },
  { key: "lastUpdated", label: "Last Updated", sortable: true },
  { key: "jobId", label: "Job ID", sortable: true },
  { key: "name", label: "Name", sortable: true },
  { key: "email", label: "Email", sortable: true },
  { key: "phone", label: "Mobile", sortable: true },
  { key: "client", label: "Client", sortable: true },
  { key: "profile", label: "Profile", sortable: true },
  { key: "skills", label: "Skills", sortable: true },
  { key: "status", label: "Status", sortable: true },
  { key: "comment", label: "Comment", sortable: true },
  { key: "peerReviewer", label: "Peer Reviewer", sortable: true },
  { key: "recruiter", label: "Recruiter", sortable: true },
];

// Suggestion priority for search
export const suggestionPriority: (keyof Candidate)[] = [
  'name',
  'email',
  'jobId',
  'client',
  'profile',
  'status',
  'recruiter',
  'phone',
  'skills',
  'peerReviewer',
  'appliedDate',
  'lastUpdated',
  'comment',
];

// Cache status selectors
export const selectCacheStatus = createSelector(
  [selectLastFetched, selectCandidates],
  (lastFetched, candidates) => {
    const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    const isCacheValid = lastFetched &&
      (new Date().getTime() - new Date(lastFetched).getTime()) < CACHE_DURATION;

    return {
      hasCache: candidates && candidates.length > 0,
      isCacheValid: !!isCacheValid,
      lastFetched,
      cacheAge: lastFetched ? new Date().getTime() - new Date(lastFetched).getTime() : null,
    };
  }
);

// Memoized selector for filtered candidates
export const selectFilteredCandidates = createSelector(
  [selectCandidates, selectAppliedTags, selectDateFilter, selectAppliedCustomDateRange],
  (candidates, appliedTags, dateFilter, appliedCustomDateRange) => {
    if (!candidates || !Array.isArray(candidates)) return [];

    const groupedTags = (appliedTags || []).reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    return candidates.filter((candidate) => {
      if (!candidate) return false;

      // Date Filter Logic
      const candidateDate = new Date(candidate.appliedDate || '');
      if (isNaN(candidateDate.getTime())) return false;

      if (dateFilter) {
        const now = new Date();
        const pastDate = new Date(now.setDate(now.getDate() - dateFilter));
        if (candidateDate < pastDate) return false;
      }

      if (appliedCustomDateRange) {
        const startDate = new Date(appliedCustomDateRange.start || '');
        const endDate = new Date(appliedCustomDateRange.end || '');
        endDate.setDate(endDate.getDate() + 1);
        if (candidateDate < startDate || candidateDate >= endDate) return false;
      }

      // Tag-based Search Logic
      if (appliedTags && appliedTags.length > 0) {
        const matchesAllGroups = Object.values(groupedTags).every(tagGroup => {
          return tagGroup.some(tag => {
            const tagValue = tag.value.toLowerCase();
            const columnInfo = candidateColumns.find(c => c.label === tag.column);

            if (tag.column === 'Any') {
              return Object.values(candidate).some(val => String(val || '').toLowerCase().includes(tagValue));
            }
            if (!columnInfo) return false;

            const candidateValue = candidate[columnInfo.key];
            return String(candidateValue || '').toLowerCase().includes(tagValue);
          });
        });

        if (!matchesAllGroups) {
          return false;
        }
      }

      return true;
    });
  }
);

// Memoized selector for sorted candidates
export const selectSortedCandidates = createSelector(
  [selectFilteredCandidates, selectSortConfig],
  (filteredCandidates, sortConfig) => {
    if (!filteredCandidates || filteredCandidates.length === 0) return [];

    return [...filteredCandidates].sort((a, b) => {
      if (!sortConfig?.key) return 0;

      let aValue: any = a?.[sortConfig.key];
      let bValue: any = b?.[sortConfig.key];

      // Handle date sorting
      if (sortConfig.key === 'appliedDate' || sortConfig.key === 'lastUpdated') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      }

      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }
);

// Memoized selector for paginated candidates
export const selectPaginatedCandidates = createSelector(
  [selectSortedCandidates, selectCurrentPage, selectItemsPerPage],
  (sortedCandidates, currentPage, itemsPerPage) => {
    if (!sortedCandidates || !currentPage || !itemsPerPage) return [];

    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return sortedCandidates.slice(indexOfFirstItem, indexOfLastItem);
  }
);

// New selectors for separating candidates based on clientAssigned
export const selectAssignedCandidates = createSelector(
  [selectFilteredCandidates],
  (candidates) => candidates?.filter(candidate => candidate?.clientAssigned === true) || []
);
export const selectAvailableCandidates = createSelector(
  [selectFilteredCandidates],
  (candidates) => candidates?.filter(candidate => candidate?.clientAssigned === false) || []
);

// Memoized selector for sorted assigned candidates
export const selectSortedAssignedCandidates = createSelector(
  [selectAssignedCandidates, selectSortConfig],
  (assignedCandidates, sortConfig) => {
    if (!assignedCandidates || assignedCandidates.length === 0) return [];

    return [...assignedCandidates].sort((a, b) => {
      if (!sortConfig?.key) return 0;

      let aValue: any = a?.[sortConfig.key];
      let bValue: any = b?.[sortConfig.key];

      // Handle date sorting
      if (sortConfig.key === 'appliedDate' || sortConfig.key === 'lastUpdated') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      }

      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }
);

// Memoized selector for paginated assigned candidates
export const selectPaginatedAssignedCandidates = createSelector(
  [selectSortedAssignedCandidates, selectCurrentPage, selectItemsPerPage],
  (sortedAssignedCandidates, currentPage, itemsPerPage) => {
    if (!sortedAssignedCandidates || !currentPage || !itemsPerPage) return [];

    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return sortedAssignedCandidates.slice(indexOfFirstItem, indexOfLastItem);
  }
);

// Memoized selector for sorted available candidates
export const selectSortedAvailableCandidates = createSelector(
  [selectAvailableCandidates, selectSortConfig],
  (availableCandidates, sortConfig) => {
    if (!availableCandidates || availableCandidates.length === 0) return [];

    return [...availableCandidates].sort((a, b) => {
      if (!sortConfig?.key) return 0;

      let aValue: any = a?.[sortConfig.key];
      let bValue: any = b?.[sortConfig.key];

      // Handle date sorting
      if (sortConfig.key === 'appliedDate' || sortConfig.key === 'lastUpdated') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      }

      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }
);

// Memoized selector for paginated available candidates
export const selectPaginatedAvailableCandidates = createSelector(
  [selectSortedAvailableCandidates, selectCurrentPage, selectItemsPerPage],
  (sortedAvailableCandidates, currentPage, itemsPerPage) => {
    if (!sortedAvailableCandidates || !currentPage || !itemsPerPage) return [];

    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return sortedAvailableCandidates.slice(indexOfFirstItem, indexOfLastItem);
  }
);

// Selector for visible columns
export const selectFilteredColumns = createSelector(
  [selectVisibleColumns],
  (visibleColumns) => {
    if (!visibleColumns) return [];
    return candidateColumns.filter(column => visibleColumns.includes(column.key));
  }
);

// Selector for search suggestions
export const selectSearchSuggestions = createSelector(
  [selectFilteredCandidates, (_: RootState, inputValue: string) => inputValue],
  (filteredCandidates, inputValue) => {
    if (!inputValue || !filteredCandidates) return [];

    const suggestions: { value: string; column: string }[] = [];

    // Use suggestion priority to order results
    suggestionPriority.forEach(key => {
      const column = candidateColumns.find(c => c.key === key);
      if (!column) return;

      filteredCandidates.forEach(candidate => {
        if (!candidate) return;

        const value = candidate[key];
        if (typeof value === 'string' && value.toLowerCase().includes(inputValue.toLowerCase())) {
          if (!suggestions.some(s => s.value === value && s.column === column.label)) {
            suggestions.push({ value, column: column.label });
          }
        }
      });
    });

    return suggestions.slice(0, 7);
  }
);

// Selector for selected candidates data
export const selectSelectedCandidatesData = createSelector(
  [selectCandidates, selectSelectedRows],
  (candidates, selectedRows) => {
    if (!candidates || !selectedRows) return [];
    return candidates.filter(candidate => candidate && selectedRows.includes(candidate.id));
  }
);

