import React, { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { useNavigate } from "react-router-dom";
import {
    Users,
    Search,
    Plus,
    Edit,
    Trash2,
    Eye,
    Check,
    X,
    UserPlus,
} from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { useUser } from "@/contexts/user-context";
import {
    selectAvailableCandidates,
    selectPaginatedAvailableCandidates,
    selectCurrentPage,
    selectItemsPerPage,
    selectSortConfig,
    selectSearchTags
} from "@/store/selectors/candidatesSelectors";
import { setCurrentPage, setItemsPerPage, setSortConfig, addSearchTag, removeSearchTag, applyFilters, clearAllFilters, fetchCandidates } from "@/store/slices/candidatesSlice";
import { fetchJobs } from "@/store/slices/jobsSlice";
import { selectJobs } from "@/store/selectors/jobsSelectors";
import { type Job } from "@/store/slices/jobsSlice";
import { type Candidate } from "@/types/candidate";
import { CandidateDetailsModal } from "@/components/modals/candidate-details-modal";
import { EditCandidateModal } from "@/components/modals/edit-candidate-modal";
import { DeleteConfirmationModal } from "@/components/modals/delete-confirmation-modal";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { toast } from "react-toastify";
import {
    createKebabMenuItem,
    type KebabMenuItem,
} from "@/components/ui/kebab-menu";
import { ApiService } from "@/services/api";

// (Job type imported from Redux slice)

// Define search tag type
type FilterTag = {
    value: string;
    column: string;
};

// Define column type for table
type Column = {
    key: keyof Candidate | "actions";
    label: string;
    sortable: boolean;
};

// CustomKebabMenu component with portal rendering and improved positioning
const CustomKebabMenu = ({ items, popupId }: { items: KebabMenuItem[]; popupId: string }) => {
    const [openPopupId, setOpenPopupId] = useState<string | null>(null);
    const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
    const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0, right: 0 });
    const menuRef = useRef<HTMLDivElement>(null);
    const submenuRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    const isOpen = openPopupId === popupId;

    // Calculate menu position to ensure it's always visible
    const updateMenuPosition = () => {
        if (buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Calculate if menu should open to the left or right
            const menuWidth = 180; // min-w-[180px]
            const menuHeight = 200; // Approximate height for positioning

            let left = 0;
            let top = rect.height + 4;

            // Check if menu would go off the right edge
            if (rect.left + menuWidth > viewportWidth) {
                // Open to the left of the button
                left = -menuWidth + rect.width;
            } else {
                // Open to the right of the button
                left = 0;
            }

            // Check if menu would go off the bottom edge
            if (rect.bottom + menuHeight > viewportHeight) {
                // Open above the button
                top = -menuHeight - 4;
            }

            setMenuPosition({ top, left, right: 0 });
        }
    };

    // Close menu when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (
                menuRef.current &&
                !menuRef.current.contains(event.target as Node) &&
                submenuRef.current &&
                !submenuRef.current.contains(event.target as Node)
            ) {
                setOpenPopupId(null);
                setActiveSubmenu(null);
            }
        }

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside);
            return () => document.removeEventListener("mousedown", handleClickOutside);
        }
    }, [isOpen, setOpenPopupId]);

    // Handle window resize to update menu position
    useEffect(() => {
        const handleResize = () => {
            if (isOpen) {
                updateMenuPosition();
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [isOpen]);

    const handleMenuItemClick = (item: any) => {
        if (item.hasSubmenu) {
            setActiveSubmenu(activeSubmenu === item.id ? null : item.id);
        } else {
            item.onClick();
            setOpenPopupId(null);
            setActiveSubmenu(null);
        }
    };

    const handleToggleMenu = () => {
        if (!isOpen) {
            updateMenuPosition();
            setOpenPopupId(popupId);
        } else {
            setOpenPopupId(null);
        }
        setActiveSubmenu(null);
    };

    return (
        <div className="relative" ref={menuRef}>
            <button
                ref={buttonRef}
                onClick={handleToggleMenu}
                className="p-1 rounded-full hover:bg-gray-100 focus:outline-none"
            >
                <svg
                    className="w-4 h-4 text-gray-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                >
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                </svg>
            </button>

            {isOpen && createPortal(
                <div
                    className="fixed z-[99999] bg-white rounded-lg shadow-2xl border border-gray-200 min-w-[180px] py-1 animate-in fade-in-0 zoom-in-95 duration-200"
                    style={{
                        top: buttonRef.current ? buttonRef.current.getBoundingClientRect().top + menuPosition.top : 0,
                        left: buttonRef.current ? buttonRef.current.getBoundingClientRect().left + menuPosition.left : 0,
                        right: menuPosition.right === 0 ? undefined : 'auto'
                    }}
                >
                    {items.map((item) => (
                        <div key={item.id} className="relative">
                            <button
                                onClick={() => handleMenuItemClick(item)}
                                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors duration-150 flex items-center gap-2 ${item.variant === "destructive"
                                    ? "text-red-600 hover:bg-red-50"
                                    : "text-gray-700"
                                    } ${item.separator ? "border-b border-gray-200" : ""}`}
                            >
                                {item.icon && <item.icon className="h-4 w-4 text-blue-500" />}
                                <span className="flex-1">{item.label}</span>
                                {item.hasSubmenu && (
                                    <svg
                                        className={`w-4 h-4 transition-transform ${activeSubmenu === item.id ? "rotate-90" : ""
                                            }`}
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                )}
                            </button>

                            {/* Submenu */}
                            {item.hasSubmenu && activeSubmenu === item.id && (
                                <div
                                    ref={submenuRef}
                                    className="absolute right-full top-0 mr-1 z-[100000] bg-white rounded-lg shadow-2xl border border-gray-200 min-w-[180px] py-1 animate-in fade-in-0 slide-in-from-left-2 duration-200"
                                >
                                    {item.submenuItems?.map((subItem: any) => (
                                        <button
                                            key={subItem.id}
                                            onClick={() => {
                                                subItem.action();
                                                setOpenPopupId(null);
                                                setActiveSubmenu(null);
                                            }}
                                            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors duration-150 flex items-center gap-2 text-gray-700"
                                        >
                                            {subItem.icon && <subItem.icon className="h-4 w-4 text-blue-500" />}
                                            <span>{subItem.label}</span>
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>
                    ))}
                </div>,
                document.body
            )}
        </div>
    );
};

// Table columns definition
const columns: Column[] = [
    { key: "name", label: "Name", sortable: true },
    { key: "email", label: "Email", sortable: true },
    { key: "phone", label: "Mobile", sortable: true },
    { key: "skills", label: "Skills", sortable: true },
    { key: "appliedDate", label: "Date", sortable: true },
    { key: "actions", label: "Actions", sortable: false },
];

export function TalentPoolPage() {
    const navigate = useNavigate();
    const { userRole, userName, userId } = useUser();
    const allCandidates = useAppSelector(selectAvailableCandidates);
    console.log(allCandidates, "allCandidates")

    // Get jobs from Redux for assignment modal
    const jobs = useAppSelector(selectJobs);

    // Local UI state for search and filtering
    const [inputValue, setInputValue] = useState("");
    // Search tags from Redux
    const searchTags = useAppSelector(selectSearchTags);
    // Sorting state from Redux
    const sortConfig = useAppSelector(selectSortConfig);

    // Pagination state from Redux
    const currentPage = useAppSelector(selectCurrentPage);
    const itemsPerPage = useAppSelector(selectItemsPerPage);

    // Modal states
    const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [candidateToDelete, setCandidateToDelete] = useState<Candidate | null>(null);
    const [selectedCandidates, setSelectedCandidates] = useState<number[]>([]);
    const [isJobsModalOpen, setIsJobsModalOpen] = useState(false);
    const [jobSearchTerm, setJobSearchTerm] = useState("");

    // Refs
    const searchContainerRef = useRef<HTMLDivElement>(null);

    // Redux dispatch
    const dispatch = useAppDispatch();

    // Animation controls
    const { isLoading, animateSorting, animatePagination } = useTableAnimation();

    // Paginated candidates from Redux
    const currentCandidates = useAppSelector(selectPaginatedAvailableCandidates);
    const totalAvailableCandidates = useAppSelector(selectAvailableCandidates).length;

    // Filter jobs based on search term (from Redux jobs) - only show active jobs
    const filteredJobs = jobs
        .filter(job => job.job_status === "Active") // Only active jobs
        .filter(job =>
            job.id.toString().toLowerCase().includes(jobSearchTerm.toLowerCase()) ||
            job.client.toLowerCase().includes(jobSearchTerm.toLowerCase()) ||
            job.role.toLowerCase().includes(jobSearchTerm.toLowerCase())
        );

    // Fetch candidates and jobs on component mount
    useEffect(() => {
        if (userId && userRole && userName) {
            const userType = userRole === "manager" ? "management" : "recruiter";
            console.log('Debug - Dispatching fetchCandidates with:', { userId, userType, userName });
            dispatch(fetchCandidates({ userId, userType, userName }));

            // Also fetch jobs for the assignment modal
            console.log('Debug - Dispatching fetchJobs with:', { username: userName });
            dispatch(fetchJobs({ username: userName }));
        } else {
            console.log('Debug - Missing user context:', { userId, userRole, userName });
        }
    }, [dispatch, userId, userRole, userName]);

    // Search and filtering handlers
    const handleAddTag = (tagOrSuggestion: string | { value: string; column: string }) => {
        const newTag: FilterTag =
            typeof tagOrSuggestion === "string"
                ? { value: tagOrSuggestion.trim(), column: "Any" }
                : {
                    value: (tagOrSuggestion.value || "").trim(),
                    column: tagOrSuggestion.column,
                };
        if (
            newTag.value &&
            !searchTags.some(
                (t) => t.value === newTag.value && t.column === newTag.column
            )
        ) {
            dispatch(addSearchTag(newTag));
        }
        setInputValue("");
    };

    const handleRemoveTag = (tagToRemove: FilterTag) => {
        dispatch(removeSearchTag(tagToRemove));
    };

    const handleApplyFilters = () => {
        dispatch(applyFilters());
        setInputValue("");
    };

    const handleClearAllFilters = () => {
        dispatch(clearAllFilters());
        setInputValue("");
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setInputValue(e.target.value);
    };

    // Sorting handler
    const handleSort = async (key: keyof Candidate | "actions") => {
        if (key === "actions") return; // Don't sort actions column
        await animateSorting();
        let direction: "ascending" | "descending" | null = "ascending";
        if (sortConfig.key === key && sortConfig.direction === "ascending") {
            direction = "descending";
        } else if (sortConfig.key === key && sortConfig.direction === "descending") {
            direction = null;
        }
        dispatch(setSortConfig({ key: key as keyof Candidate, direction }));
        dispatch(setCurrentPage(1)); // Reset to first page when sorting
    };

    // Pagination handler
    const paginate = async (pageNumber: number) => {
        await animatePagination();
        dispatch(setCurrentPage(pageNumber));
    };

    // Close modals
    const closeCandidateDetails = () => setIsDetailsModalOpen(false);
    const closeEditCandidate = () => setIsEditModalOpen(false);
    const closeDeleteModal = () => setIsDeleteModalOpen(false);
    const closeJobsModal = () => setIsJobsModalOpen(false);

    // Handle candidate actions
    const handleViewCandidate = (candidate: Candidate) => {
        setSelectedCandidate(candidate);
        setIsDetailsModalOpen(true);
    };

    const handleEditCandidate = (candidate: Candidate) => {
        setSelectedCandidate(candidate);
        setIsEditModalOpen(true);
    };

    const handleDeleteCandidate = (candidate: Candidate) => {
        setCandidateToDelete(candidate);
        setIsDeleteModalOpen(true);
    };

    const handleAddToTalentPool = () => {
        navigate('/register-candidate', {
            state: {
                isTalentPoolMode: true,
                client: "Talent Pool",
                role: "Talent Pool"
            }
        });
    };

    const handleSubmitCandidates = () => {
        if (selectedCandidates.length > 0) {
            setIsJobsModalOpen(true);
        }
    };

    const handleAssignToJob = async (job: Job) => {
        try {
            const results = await Promise.all(selectedCandidates.map(async (candidateId) => {
                const candidate = allCandidates.find(c => c.id === candidateId);
                if (!candidate) return { status: 'error', message: `Candidate ${candidateId} not found` };
                // Prepare check_candidate payload
                const checkPayload = {
                    name: candidate.name,
                    mobile: candidate.phone,
                    email: candidate.email,
                    total_experience_years: String(Math.floor(candidate.experience)),
                    total_experience_months: String(Math.round((candidate.experience % 1) * 12)),
                    relevant_experience_years: String(Math.floor(candidate.experience)),
                    relevant_experience_months: String(Math.round((candidate.experience % 1) * 12)),
                    qualifications: candidate.education,
                    last_working_date: '', // Not available in Candidate type
                    serving_notice_period: '', // Not available in Candidate type
                };
                const checkRes = await ApiService.checkCandidate(checkPayload);
                // Check if candidate is already present for this job/client/profile
                if (
                    checkRes &&
                    checkRes.jobIds &&
                    checkRes.clients &&
                    checkRes.profiles &&
                    checkRes.jobIds.includes(job.id) &&
                    checkRes.clients.includes(job.client) &&
                    checkRes.profiles.includes(job.role)
                ) {
                    return { status: 'exists', message: `Candidate already assigned to this job/client/profile.` };
                }
                // If not present, assign
                const payload = {
                    id: candidate.id,
                    job_id: job.id,
                    client: job.client,
                    profile: job.role,
                };
                return ApiService.assignClientToJob(payload);
            }));
            if (results.every(res => res.status === 'success')) {
                toast.success('Candidates assigned successfully!');
            } else if (results.some(res => res.status === 'exists')) {
                toast.error("Some candidates were already assigned to this job/client/profile.")
            } else {
                toast.error('Some assignments failed.');
            }
        } catch (error) {
            toast.error('Error assigning candidates: ' + (error instanceof Error ? error.message : 'Unknown error'));
        }
        closeJobsModal();
    };

    const handleCheckboxChange = (candidateId: number) => {
        setSelectedCandidates(prevSelected => {
            if (prevSelected.includes(candidateId)) {
                return prevSelected.filter(id => id !== candidateId);
            } else {
                return [...prevSelected, candidateId];
            }
        });
    };

    // Create kebab menu items for candidates
    const createCandidateMenuItems = (candidate: Candidate): KebabMenuItem[] => [
        createKebabMenuItem(
            "view-details",
            "View Details",
            () => handleViewCandidate(candidate),
            { icon: Eye }
        ),
        createKebabMenuItem(
            "edit-candidate",
            "Edit Candidate",
            () => handleEditCandidate(candidate),
            { icon: Edit }
        ),
        createKebabMenuItem(
            "delete-candidate",
            "Delete Candidate",
            () => handleDeleteCandidate(candidate),
            { icon: Trash2, variant: "destructive" }
        ),
    ];

    return (
        <div className="flex flex-col p-4">
            <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
                <div className="flex-1 min-w-[300px] relative" ref={searchContainerRef}>
                    <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500">
                        <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
                        {searchTags.map((tag) => (
                            <span
                                key={`${tag.column}-${tag.value}`}
                                className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                            >
                                <span className="font-normal text-blue-600 mr-1">
                                    {tag.column}:
                                </span>
                                {tag.value}
                                <button
                                    onClick={() => handleRemoveTag(tag)}
                                    className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"
                                >
                                    <X className="h-3.5 w-3.5" />
                                </button>
                            </span>
                        ))}
                        <input
                            type="text"
                            className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none"
                            placeholder="Search and add filters..."
                            value={inputValue}
                            onChange={handleInputChange}
                            onKeyDown={(e) => {
                                if (e.key === "Enter" && inputValue.trim()) {
                                    e.preventDefault();
                                    handleAddTag(inputValue);
                                }
                            }}
                        />
                    </div>
                    <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
                        <button
                            onClick={handleApplyFilters}
                            title="Apply Filters"
                            className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600"
                        >
                            <Check className="h-5 w-5" />
                        </button>
                        <button
                            onClick={handleClearAllFilters}
                            title="Clear All Filters"
                            className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500"
                        >
                            <X className="h-5 w-5" />
                        </button>
                    </div>
                </div>
                <div className="flex flex-wrap items-center gap-3">
                    {/* Submit Candidate Button */}
                    <button
                        onClick={handleSubmitCandidates}
                        disabled={selectedCandidates.length === 0}
                        className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 transition-colors"
                        title={selectedCandidates.length === 0 ? "Select candidates first" : `Submit ${selectedCandidates.length} selected candidate(s)`}
                    >
                        <UserPlus className="h-4 w-4" />
                        Submit Candidate
                        {selectedCandidates.length > 0 && (
                            <span className="bg-white text-green-600 px-2 py-0.5 rounded-full text-xs font-bold">
                                {selectedCandidates.length}
                            </span>
                        )}
                    </button>
                    <button
                        onClick={handleAddToTalentPool}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 transition-colors"
                    >
                        <Plus className="h-4 w-4" />
                        Add New Candidate
                    </button>
                    <select
                        className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500 min-w-[120px]"
                        onChange={async (e) => {
                            try {
                                const newItemsPerPage = parseInt(e.target.value, 10);
                                if (isNaN(newItemsPerPage) || newItemsPerPage <= 0) return;
                                await animatePagination();
                                dispatch(setItemsPerPage(newItemsPerPage));
                                dispatch(setCurrentPage(1));
                            } catch (error) {
                                console.error('Error updating items per page:', error);
                            }
                        }}
                        value={itemsPerPage}
                    >
                        <option value={10}>10 per page</option>
                        <option value={20}>20 per page</option>
                        <option value={50}>50 per page</option>
                    </select>
                </div>
            </div>

            {/* Candidates Table */}
            <AnimatedTableWrapper
                isLoading={isLoading}
                loadingComponent={<TableSkeleton rows={8} cols={columns.length + 1} />}
                className="border border-gray-200 rounded-md overflow-visible flex-1"
            >
                <div className="overflow-auto max-h-[67vh] sm:h-[500px] relative table-scrollbar">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                {/* Select All Checkbox */}
                                <th
                                    scope="col"
                                    className="px-2 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200"
                                >
                                    <input
                                        type="checkbox"
                                        checked={selectedCandidates.length === currentCandidates.length && currentCandidates.length > 0}
                                        onChange={(e) => {
                                            if (e.target.checked) {
                                                setSelectedCandidates(currentCandidates.map(c => c.id));
                                            } else {
                                                setSelectedCandidates([]);
                                            }
                                        }}
                                        className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                                    />
                                </th>
                                {columns.map((column) => (
                                    <th
                                        key={String(column.key)}
                                        scope="col"
                                        className={`px-2 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200 ${column.key === "skills" ? "w-[200px] max-w-[200px]" : ""
                                            }`}
                                        onClick={() => column.sortable && handleSort(column.key)}
                                    >
                                        <div className="flex items-center gap-1">
                                            {column.label}
                                            <AnimatedSortIcon
                                                direction={
                                                    sortConfig.key === column.key
                                                        ? sortConfig.direction
                                                        : null
                                                }
                                                active={sortConfig.key === column.key}
                                                size={14}
                                            />
                                        </div>
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {totalAvailableCandidates === 0 ? (
                                <tr>
                                    <td
                                        colSpan={columns.length + 1}
                                        className="text-center py-4 text-xs text-gray-500"
                                    >
                                        No candidates.
                                    </td>
                                </tr>
                            ) : (
                                currentCandidates.map((candidate, index) => (
                                    <AnimatedTableRow key={candidate.id} index={index}>
                                        {/* Candidate Selection Checkbox */}
                                        <td className="px-2 py-2 text-center">
                                            <input
                                                type="checkbox"
                                                checked={selectedCandidates.includes(candidate.id)}
                                                onChange={() => handleCheckboxChange(candidate.id)}
                                                className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                                            />
                                        </td>
                                        {/* FIX: No whitespace here */}
                                        {columns.map((column) => {
                                            const cellClassName =
                                                "px-2 py-2 text-xs text-gray-800 font-medium";
                                            if (column.key === "name") {
                                                return (
                                                    <td
                                                        key={`${candidate.id}-name`}
                                                        className={cellClassName}
                                                    >
                                                        <div className="flex items-center">
                                                            <div className="ml-3">
                                                                <div className="text-xs font-medium text-gray-900">
                                                                    {candidate.name}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                );
                                            }
                                            if (column.key === "email") {
                                                return (
                                                    <td
                                                        key={`${candidate.id}-email`}
                                                        className={`${cellClassName} max-w-xs`}
                                                    >
                                                        <span
                                                            className="block truncate"
                                                            title={candidate.email}
                                                        >
                                                            {candidate.email}
                                                        </span>
                                                    </td>
                                                );
                                            }
                                            if (column.key === "phone") {
                                                return (
                                                    <td
                                                        key={`${candidate.id}-phone`}
                                                        className={cellClassName}
                                                    >
                                                        {candidate.phone}
                                                    </td>
                                                );
                                            }
                                            if (column.key === "skills") {
                                                return (
                                                    <td
                                                        key={`${candidate.id}-skills`}
                                                        className={`${cellClassName} skills-column`}
                                                    >
                                                        <div className="max-w-full">
                                                            <span
                                                                className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-bold bg-blue-100 text-blue-800 max-w-full truncate"
                                                                title={candidate.skills}
                                                            >
                                                                {candidate.skills}
                                                            </span>
                                                        </div>
                                                    </td>
                                                );
                                            }
                                            if (column.key === "appliedDate") {
                                                return (
                                                    <td
                                                        key={`${candidate.id}-date`}
                                                        className={cellClassName}
                                                    >
                                                        {new Date(candidate.appliedDate).toLocaleDateString()}
                                                    </td>
                                                );
                                            }
                                            if (column.key === "actions") {
                                                return (
                                                    <td key={`${candidate.id}-actions`} className="px-2 py-2 text-xs text-center">
                                                        <CustomKebabMenu items={createCandidateMenuItems(candidate)} popupId={`candidate-${candidate.id}`} />
                                                    </td>
                                                );
                                            }
                                            return (
                                                <td
                                                    key={`${candidate.id}-${String(column.key)}`}
                                                    className={`${cellClassName} whitespace-nowrap `}
                                                >
                                                    {String(candidate[column.key as keyof Candidate] || "")}
                                                </td>
                                            );
                                        })}
                                    </AnimatedTableRow>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </AnimatedTableWrapper>
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mt-2 text-sm text-gray-700 gap-4 bg-white p-2 rounded-md border border-gray-200">
                <div className="text-xs sm:text-sm">
                    {totalAvailableCandidates > 0 ? (
                        <>
                            Showing{" "}
                            {Math.min(
                                1 + (currentPage - 1) * itemsPerPage,
                                totalAvailableCandidates
                            )}{" "}
                            to {Math.min(currentPage * itemsPerPage, totalAvailableCandidates)} of{" "}
                            {totalAvailableCandidates} candidates
                        </>
                    ) : (
                        "No candidates found"
                    )}
                </div>
                <div className="w-full sm:w-auto flex justify-center sm:justify-end">
                    <AnimatedPagination
                        currentPage={currentPage}
                        totalPages={Math.ceil(totalAvailableCandidates / itemsPerPage)}
                        onPageChange={paginate}
                    />
                </div>
            </div>

            {/* Modals */}
            <CandidateDetailsModal
                candidate={selectedCandidate}
                isOpen={isDetailsModalOpen}
                onClose={closeCandidateDetails}
            />

            <EditCandidateModal
                candidate={selectedCandidate}
                isOpen={isEditModalOpen}
                onClose={closeEditCandidate}
                onSave={(updatedCandidate) => {
                    console.log("Updated candidate:", updatedCandidate);
                    closeEditCandidate();
                }}
            />

            <DeleteConfirmationModal
                isOpen={isDeleteModalOpen}
                onClose={closeDeleteModal}
                onConfirm={() => {
                    if (candidateToDelete) {
                        console.log("Deleting candidate:", candidateToDelete.id);
                        closeDeleteModal();
                    }
                }}
                candidate={candidateToDelete}
            />

            {/* Available Jobs Modal - Slides from right to center */}
            {isJobsModalOpen && (
                <div className="fixed inset-0 z-50 flex justify-end">
                    <div className="h-full w-150 bg-white shadow-2xl transform transition-transform duration-300 ease-in-out">
                        <div className="h-full flex flex-col">
                            {/* Header */}
                            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900">
                                        Select Job for Assignment
                                    </h3>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Assign {selectedCandidates.length} candidate{selectedCandidates.length !== 1 ? 's' : ''} to a job
                                    </p>
                                </div>
                                <button
                                    onClick={closeJobsModal}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <X className="w-6 h-6" />
                                </button>
                            </div>

                            {/* Search */}
                            <div className="p-4 border-b border-gray-200">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="Search by Job ID, Client, or Role"
                                        value={jobSearchTerm}
                                        onChange={(e) => setJobSearchTerm(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                            </div>

                            {/* Jobs List */}
                            <div className="flex-1 overflow-y-auto">
                                <div className="p-4">
                                    {/* Header with stats */}
                                    <div className="flex items-center justify-between mb-6">
                                        <div>
                                            <h4 className="text-xl font-bold text-gray-900">Available Jobs</h4>
                                            <p className="text-gray-600 mt-1">Choose a job to assign the selected candidate(s)</p>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-2xl font-bold text-blue-600">
                                                {filteredJobs.length}
                                            </div>
                                            <div className="text-sm text-gray-500">Active Jobs</div>
                                        </div>
                                    </div>

                                    {/* Jobs Table */}
                                    {filteredJobs.length === 0 ? (
                                        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                                            {/* Table Header */}
                                            <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                                                <div className="grid grid-cols-4 gap-4 text-sm font-medium text-gray-700">
                                                    <div>Job ID</div>
                                                    <div>Client</div>
                                                    <div>Role</div>
                                                    <div>Positions</div>
                                                </div>
                                            </div>

                                            {/* Empty Table Body */}
                                            <div className="text-center py-16">
                                                <div className="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                                    <Users className="w-8 h-8 text-gray-400" />
                                                </div>
                                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                    {jobSearchTerm ? 'No matches found' : 'No active jobs available'}
                                                </h3>
                                                <p className="text-gray-500 mb-4 text-sm">
                                                    {jobSearchTerm
                                                        ? 'Try different search terms or check your filters'
                                                        : 'No active jobs are currently available for assignment'
                                                    }
                                                </p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                                            {/* Table Header */}
                                            <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                                                <div className="grid grid-cols-5 gap-4 text-sm font-medium text-gray-700">
                                                    <div>#</div>
                                                    <div>Job ID</div>
                                                    <div>Client</div>
                                                    <div>Role</div>
                                                    <div>Actions</div>
                                                </div>
                                            </div>

                                            {/* Table Body */}
                                            <div className="divide-y divide-gray-200">
                                                {filteredJobs.map((job, index) => (
                                                    <div
                                                        key={job.id}
                                                        className="group px-4 py-3 hover:bg-gray-50 transition-colors cursor-pointer"
                                                        onClick={() => handleAssignToJob(job)}
                                                    >
                                                        <div className="grid grid-cols-5 gap-4 items-center">
                                                            {/* Number */}
                                                            <div className="text-sm font-medium text-gray-600">
                                                                {index + 1}
                                                            </div>

                                                            {/* Job ID */}
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {job.id}
                                                                <div className="flex items-center gap-2 mt-1">
                                                                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                                                        {parseInt(job.no_of_positions) || 0} Position{(parseInt(job.no_of_positions) || 0) !== 1 ? 's' : ''}
                                                                    </span>

                                                                </div>
                                                            </div>

                                                            {/* Client */}
                                                            <div className="text-sm text-gray-600 truncate">
                                                                {job.client}
                                                            </div>

                                                            {/* Role */}
                                                            <div className="text-sm text-gray-600 truncate">
                                                                {job.role}
                                                            </div>

                                                            {/* Actions */}
                                                            <div className="flex-shrink-0">
                                                                <button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        handleAssignToJob(job);
                                                                    }}
                                                                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors"
                                                                >
                                                                    Assign
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}    