import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import { type User } from "@/store/slices/activeUsersSlice";

interface DeleteUserConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  user: User | null;
  loading?: boolean;
}

export function DeleteUserConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  user,
  loading = false,
}: DeleteUserConfirmationModalProps) {
  if (!user) return null;

  const displayName = user.firstName && user.lastName
    ? `${user.firstName} ${user.lastName}`
    : user.name || user.username;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Confirm User Deletion
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-gray-700 mb-4">
            Are you sure you want to delete this user? This action cannot be undone and will permanently remove the user from the system.
          </p>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm space-y-1">
              <p><strong>Name:</strong> {displayName}</p>
              <p><strong>Username:</strong> {user.username}</p>
              {/* <p><strong>Email:</strong> {user.email}</p>
              <p><strong>User Type:</strong> {user.userType === 'management' ? 'Manager' : 'Recruiter'}</p>
              <p><strong>Status:</strong>
                <span className={`ml-1 ${user.isActive ? 'text-green-600' : 'text-red-600'}`}>
                  {user.isActive ? 'Active' : 'Inactive'}
                </span>
              </p>
              <p><strong>Verified:</strong>
                <span className={`ml-1 ${user.isVerified ? 'text-green-600' : 'text-orange-600'}`}>
                  {user.isVerified ? 'Yes' : 'No'}
                </span>
              </p>
              <p><strong>Peer Reviewer:</strong>
                <span className={`ml-1 ${user.isPeerReviewer ? 'text-blue-600' : 'text-gray-600'}`}>
                  {user.isPeerReviewer ? 'Yes' : 'No'}
                </span>
              </p> */}
            </div>
          </div>

          {/* <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <strong>Warning:</strong> Deleting this user will:
            </p>
            <ul className="text-sm text-red-700 mt-2 ml-4 list-disc">
              <li>Remove all user data permanently</li>
              <li>Revoke all access permissions</li>
              <li>Cannot be undone</li>
            </ul>
          </div> */}
        </div>

        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700"
          >
            {loading ? "Deleting..." : "Delete User"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
