import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff, LogIn, AlertCircle } from "lucide-react";
import { useUser, UserRole } from "@/contexts/user-context";
import { LoginCredentials } from "@/services/auth";
import login8 from "../../assets/login8.png";

export function Login() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedRole, setSelectedRole] = useState<UserRole>(null);
  const [capsLock, setCapsLock] = useState(false);

  const { login } = useUser();
  const navigate = useNavigate();

  // Check for role parameter in URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const roleParam = params.get("role") as UserRole;
    if (roleParam === "manager" || roleParam === "recruiter") {
      setSelectedRole(roleParam);
    }
    const saved = localStorage.getItem("rememberedUsername");
    if (saved) {
      setUsername(saved);
      setRememberMe(true);
    }
  }, []);

  const usernameValid = useMemo(() => {
    if (!username) return false;
    const re = /^[a-zA-Z0-9._-]{3,}$/;
    return re.test(username);
  }, [username]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!username || !password) {
      setError("Please enter both username and password");
      return;
    }

    if (!selectedRole) {
      setError("Please select a role");
      return;
    }

    try {
      setIsLoading(true);

      const credentials: LoginCredentials = {
        username,
        password,
      };

      // Call the login function from context
      await login(credentials, selectedRole);

      // Navigate to role-specific dashboard after successful login
      if (selectedRole === "manager") {
        navigate("/manager/dashboard");
      } else if (selectedRole === "recruiter") {
        navigate("/recruiter/dashboard");
      }
    } catch (error: any) {
      setError(error.message || "Login failed. Please check your credentials.");
    } finally {
      setIsLoading(false);
    }
  };

  const onPasswordKey = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const isCaps = e.getModifierState && e.getModifierState("CapsLock");
    setCapsLock(isCaps);
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center bg-cover bg-center px-4 relative"
      style={{ backgroundImage: `url(${login8})` }}
    >


      <div className="absolute inset-0 opacity-20">
        <div className="w-full h-full bg-[radial-gradient(circle_at_1px_1px,rgba(156,146,172,0.15)_1px,transparent_0)] bg-[length:20px_20px]"></div>
      </div>
      <div className="w-full max-w-sm bg-white rounded-2xl shadow-xl overflow-hidden relative z-10">
        <div className="px-6 py-6 text-white bg-[#0A2647]">
          <h2 className="text-2xl font-bold text-center text-white">
            {selectedRole === "manager"
              ? "Manager Portal"
              : selectedRole === "recruiter"
                ? "Recruiter Portal"
                : "Welcome Back"}
          </h2>
          <p className="mt-1 text-sm text-center opacity-90">
            Sign in to your account
          </p>
        </div>

        {error && (
          <div className="mx-6 mt-4 flex items-start space-x-2 p-3 rounded-lg bg-red-50 text-red-700 border border-red-200">
            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <p className="text-xs">{error}</p>
          </div>
        )}

        <form className="px-6 py-6 space-y-4" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Role Selection */}
            {!selectedRole && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Role
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    type="button"
                    onClick={() => setSelectedRole("manager")}
                    className="p-3 border border-gray-300 rounded-lg hover:bg-blue-50 hover:border-blue-500 transition-all text-center"
                  >
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-blue-600"
                      >
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <span className="text-xs font-medium text-gray-900">
                      Manager
                    </span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedRole("recruiter")}
                    className="p-3 border border-gray-300 rounded-lg hover:bg-green-50 hover:border-green-500 transition-all text-center"
                  >
                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-green-600"
                      >
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M19 8v6"></path>
                        <path d="M16 11h6"></path>
                      </svg>
                    </div>
                    <span className="text-xs font-medium text-gray-900">
                      Recruiter
                    </span>
                  </button>
                </div>
              </div>
            )}

            {/* Selected Role Display */}
            {selectedRole && (
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div
                    className={`w-6 h-6 rounded-full ${selectedRole === "manager"
                      ? "bg-blue-100"
                      : "bg-green-100"
                      } flex items-center justify-center mr-2`}
                  >
                    {selectedRole === "manager" ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-blue-600"
                      >
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-green-600"
                      >
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M19 8v6"></path>
                        <path d="M16 11h6"></path>
                      </svg>
                    )}
                  </div>
                  <span
                    className={`text-sm font-medium ${selectedRole === "manager"
                      ? "text-blue-600"
                      : "text-green-600"
                      }`}
                  >
                    {selectedRole === "manager" ? "Manager" : "Recruiter"}
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => setSelectedRole(null)}
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  Change
                </button>
              </div>
            )}

            <div>
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Username
              </label>
              <input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                required
                aria-invalid={!usernameValid}
                className={`appearance-none block w-full px-3 py-2 border rounded-lg placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 transition-colors text-sm ${usernameValid ? 'border-gray-300 focus:ring-blue-500 focus:border-blue-500' : 'border-red-300 focus:ring-red-500 focus:border-red-500'}`}
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
              {!usernameValid && username && (
                <p className="mt-1 text-xs text-red-600">3+ chars; letters, numbers, dot, dash, underscore only.</p>
              )}
            </div>
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  onKeyUp={onPasswordKey}
                  aria-invalid={!!password && password.length < 8}
                  className={`appearance-none block w-full px-3 py-2 border rounded-lg placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 transition-colors pr-10 text-sm ${password && password.length < 8 ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'}`}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {capsLock && (
                <p className="mt-1 text-xs text-amber-600">Caps Lock is on</p>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-gray-700"
              >
                Remember me
              </label>
            </div>

            <div>
              <a
                href="#"
                className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
              >
                Forgot password?
              </a>
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2.5 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-[#0A2647] hover:bg-[#0d305d] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all">

            {isLoading ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Signing in...
              </>
            ) : (
              <>
                <LogIn className="h-4 w-4 mr-2" />
                Sign In
              </>
            )}
          </button>

          <div className="text-center mt-4">
            <p className="text-xs text-gray-600">
              Don't have an account?{" "}
              <a
                href={`/register${selectedRole ? `?role=${selectedRole}` : ""}`}
                onClick={(e) => {
                  e.preventDefault();
                  navigate(`/register${selectedRole ? `?role=${selectedRole}` : ""}`);
                }}
                className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
              >
                Register
              </a>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}
