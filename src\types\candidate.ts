// Define the Candidate type with all properties from API
export interface Candidate {
  // Basic identification
  id: number;
  jobId: string;
  name: string;
  email: string;
  phone: string;

  // Job and client information
  client: string;
  profile: string;
  status: string;
  appliedDate: string;
  source: string;

  // Professional details
  experience: number;
  education: string;
  location: string;
  skills: string;
  salary: string;
  notes: string;
  lastUpdated: string;
  comment: string;

  // Team information
  peerReviewer: string;
  recruiter: string;
  management: string | null;

  // Additional API fields that were missing
  currentCompany: string;
  position: string;
  currentJobLocation: string;
  preferredJobLocation: string;
  relevantExperience: number;
  currentCTC: string;
  expectedCTC: string;
  noticePeriod: string;
  linkedin: string;
  reasonForJobChange: string;
  holdingOffer: string;
  servingNoticePeriod: string;
  periodOfNotice: string;
  lastWorkingDate: string | null;
  totalOffers: number | null;
  highestPackageInLpa: number | null;
  buyout: boolean;
  resumePresent: boolean;
  userId: number;
  clientAssigned: boolean;

  // Comments system
  comments?: {
    [userName: string]: Array<{
      comment: string;
      timestamp: string;
    }>;
  };
}

// Define column configuration
export interface Column {
  key: keyof Candidate;
  label: string;
  sortable: boolean;
}
