# Edit Job Modal File Handling Implementation

## Overview

The edit job modal has been enhanced to handle file uploads and display existing files when editing a job. The implementation follows the exact pattern requested, including the API call to `/get_job_data/${jobId}` and proper file state management.

## Key Features

### 1. File State Management
```typescript
const [selectedFiles, setSelectedFiles] = useState<File[]>([]); // Holds newly selected files
const [existingFiles, setExistingFiles] = useState<JobFileData[]>([]); // Existing files from API
const [removedFileIds, setRemovedFileIds] = useState<number[]>([]); // Track removed files
```

### 2. API Integration
- **Get Job Data**: `GET /get_job_data/${jobId}` - Fetches job details with existing files
- **Edit Job**: `POST /edit_job_post/${jobId}` - Updates job with new files and removed file IDs

### 3. File Handling Functions

#### File Selection
```typescript
const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSelectedFiles(files); // Store new files
};
```

#### Remove Existing File
```typescript
const handleRemoveFile = (fileId: number) => {
    console.log(fileId, "fileId");
    setExistingFiles((prev) => prev.filter((file) => file.id !== fileId));
    setRemovedFileIds((prev) => [...prev, fileId]); // Track removed files
};
```

#### Remove New File
```typescript
const handleRemoveFileNew = (indexToRemove: number) => {
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToRemove);
    setSelectedFiles(updatedFiles);
};
```

#### File to Base64 Conversion
```typescript
const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve((reader.result as string).split(",")[1]);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
};
```

## API Interfaces

### GetJobDataResponse
```typescript
export interface GetJobDataResponse {
  status: string;
  message?: string;
  job_data?: any; // The complete job object
  files?: JobFileData[];
  jd_pdf?: string; // Base64 encoded main JD file
  pdfs?: JobFileData[]; // Additional PDF files
  file_type?: string;
  existing_file_ids?: number[];
}
```

### JobFileData
```typescript
export interface JobFileData {
  id: number;
  filename: string;
  extension: string;
  file_size?: number;
  upload_date?: string;
}
```

## UI Implementation

The file upload section follows the exact layout requested:

```jsx
<div style={{ display: "flex", gap: "20px", width: "100%" }}>
    <div className="JS" style={{ width: "50%", maxHeight: "160px", overflowY: "auto" }}>
        <label htmlFor="jd_pdf">
            <span style={{ color: "red" }}>*</span> Detailed JD:
        </label>

        {/* File Upload Input */}
        <Input
            type="file"
            id="jd_pdf"
            accept=".pdf,.doc,.docx"
            multiple
            onChange={handleFileChange}
            className="mt-1"
        />

        {/* Files Preview */}
        {(existingFiles.length > 0 || selectedFiles.length > 0) && (
            <div style={{
                textAlign: "left",
                maxHeight: "150px",
                overflowY: "auto",
                padding: "8px",
                borderRadius: "5px",
                backgroundColor: "#fafafa",
                marginTop: "10px",
            }}>
                <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
                    {/* Existing Files */}
                    {existingFiles.map((file, index) => (
                        <li key={`existing-${index}`} style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            marginBottom: "4px",
                        }}>
                            <span style={{ color: "green", fontSize: "15px" }}>
                                {file.filename}
                            </span>
                            <button
                                type="button"
                                onClick={() => handleRemoveFile(file.id)}
                                style={{
                                    background: "transparent",
                                    border: "none",
                                    color: "red",
                                    fontWeight: "bold",
                                    cursor: "pointer",
                                    fontSize: "14px",
                                }}
                            >
                                ❌
                            </button>
                        </li>
                    ))}
                    
                    {/* New Files */}
                    {selectedFiles.map((file, index) => (
                        <li key={`selected-${index}`} style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            marginBottom: "4px",
                        }}>
                            <span style={{ fontSize: "15px" }}>{file.name}</span>
                            <button
                                type="button"
                                onClick={() => handleRemoveFileNew(index)}
                                style={{
                                    background: "transparent",
                                    border: "none",
                                    color: "red",
                                    fontWeight: "bold",
                                    cursor: "pointer",
                                    fontSize: "14px",
                                }}
                            >
                                ❌
                            </button>
                        </li>
                    ))}
                </ul>
            </div>
        )}
    </div>

    {/* Recruiter Section */}
    <div className="JS posit" style={{ width: "50%" }} id='recruiter'>
        <Label htmlFor="recruiter" id='recru'>
            <span className="required-field" style={{ marginTop: "-30px" }}>*</span>Recruiter:
        </Label>
        <Input
            id="recruiter"
            value={formData.recruiter}
            onChange={(e) => handleInputChange("recruiter", e.target.value)}
            placeholder="Enter recruiter name"
            required
            className="mt-1"
        />
    </div>
</div>
```

## Submit Handler

The submit handler processes both new and existing files:

```typescript
// Convert selected files to base64
const pdfs = await Promise.all(
    selectedFiles.map(async (file) => ({
        filename: file.name,
        file_data: await fileToBase64(file),
        extension: file.name.split(".").pop() || "",
    }))
);

// Get existing file IDs (excluding removed ones)
const existingFileIds = existingFiles
    .filter(file => !removedFileIds.includes(file.id))
    .map(file => file.id);

const requestData: EditJobRequest = {
    // ... other form data
    existing_file_ids: existingFileIds.length > 0 ? existingFileIds : undefined,
    pdfs: pdfs.length > 0 ? pdfs : undefined,
};
```

## Data Flow

1. **Modal Opens**: Calls `fetchJobDataWithFiles(job.id)` to get existing files
2. **API Response**: Populates `existingFiles` state with file data
3. **User Interaction**: 
   - Select new files → Updates `selectedFiles`
   - Remove existing file → Updates `existingFiles` and `removedFileIds`
   - Remove new file → Updates `selectedFiles`
4. **Submit**: Converts new files to base64 and sends both new files and existing file IDs to API

## File Display

- **Existing files**: Shown in green color to indicate they're already uploaded
- **New files**: Shown in default color
- **Remove buttons**: ❌ emoji for both existing and new files
- **Scrollable container**: Max height with overflow for many files

This implementation exactly matches the requested functionality and follows the established patterns in the application.
