import { useEffect, useRef, useCallback, useState } from 'react';
import { useUser } from '@/contexts/user-context';
import { sseService, SSEEvent } from '@/services/sse';
import { useDataManagement } from './use-data-management';
import { toast } from 'react-toastify';

interface UseRealtimeUpdatesOptions {
    enableSSE?: boolean;
    enablePolling?: boolean;
    pollingInterval?: number; // in milliseconds
    showNotifications?: boolean;
}

interface UseRealtimeUpdatesReturn {
    isConnected: boolean;
    connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
    lastUpdate: Date | null;
    reconnect: () => void;
}

export const useRealtimeUpdates = (options: UseRealtimeUpdatesOptions = {}): UseRealtimeUpdatesReturn => {
    const {
        enableSSE = true,
        enablePolling = true,
        pollingInterval = 30000, // 30 seconds
        showNotifications = true
    } = options;

    const { userId, userRole, isAuthenticated } = useUser();
    const { refreshCandidates, refreshJobs, refreshActiveUsers } = useDataManagement();

    const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting' | 'error'>('disconnected');
    const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

    const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
    const eventListenerRef = useRef<((event: SSEEvent) => void) | null>(null);

    // Handle SSE events
    const handleSSEEvent = useCallback((event: SSEEvent) => {
        setLastUpdate(new Date());
         console.log("🔄 SSE Event Received:", event);
        if (showNotifications) {
            switch (event.type) {
                case 'candidate_update':
                    // Use your existing notification style
                    toast.info(`✏️ Candidate "${event.data.name || 'Unknown'}" has been updated by ${event.data.updatedBy}`, {
                        position: 'top-right',
                        autoClose: 3000,
                        theme: 'colored',
                    });
                    break;
                case 'job_update':
                    toast.info(`Job "${event.data.role || 'Unknown'}" has been updated by ${event.data.updatedBy}`, {
                        position: 'top-right',
                        autoClose: 3000,
                        theme: 'colored',
                    });
                    break;
                case 'profile_update':
                    toast.info(`✅ Peer review status updated for "${event.data.name || 'Unknown'}" by ${event.data.updatedBy}`, {
                        position: 'top-right',
                        autoClose: 3000,
                        theme: 'colored',
                    });
                    break;
                case 'system_message':
                    toast.info(event.data.message || 'System notification', {
                        position: 'top-right',
                        autoClose: 5000,
                        theme: 'colored',
                    });
                    break;
            }
        }
    }, [showNotifications]);

    // Start polling as fallback (only when SSE is not connected)
    const startPolling = useCallback(() => {
        if (!enablePolling || !isAuthenticated) return;

        const poll = async () => {
            // Only poll if SSE is not connected
            if (sseService.getConnectionStatus()) {
                return;
            }

            try {
                // Refresh data periodically - use force=false to respect cache
                await Promise.all([
                    refreshCandidates(false), // Don't force, respect cache
                    refreshJobs(false), // Don't force, respect cache
                    userRole === 'manager' && refreshActiveUsers() // No parameters needed
                ].filter(Boolean));

                setLastUpdate(new Date());
            } catch (error) {
                console.error('Polling error:', error);
            }
        };

        // Set up interval
        pollingIntervalRef.current = setInterval(poll, pollingInterval);
    }, [enablePolling, isAuthenticated, refreshCandidates, refreshJobs, refreshActiveUsers, userRole, pollingInterval]);

    // Stop polling
    const stopPolling = useCallback(() => {
        if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
        }
    }, []);

    // Connect to SSE
    const connectSSE = useCallback(() => {
        if (!enableSSE || !userId || !userRole || !isAuthenticated) return;

        setConnectionStatus('connecting');

        try {
            // Add event listener
            eventListenerRef.current = handleSSEEvent;
            sseService.addEventListener(handleSSEEvent);

            // Connect to SSE
            sseService.connect(userId, userRole);

            // Check connection status periodically
            const checkConnection = () => {
                const isConnected = sseService.getConnectionStatus();
                setConnectionStatus(isConnected ? 'connected' : 'disconnected');

                if (!isConnected) {
                    setTimeout(checkConnection, 1000);
                }
            };

            setTimeout(checkConnection, 1000);

        } catch (error) {
            console.error('SSE connection error:', error);
            setConnectionStatus('error');
        }
    }, [enableSSE, userId, userRole, isAuthenticated, handleSSEEvent]);

    // Disconnect from SSE
    const disconnectSSE = useCallback(() => {
        if (eventListenerRef.current) {
            sseService.removeEventListener(eventListenerRef.current);
            eventListenerRef.current = null;
        }
        sseService.disconnect();
        setConnectionStatus('disconnected');
    }, []);

    // Reconnect function
    const reconnect = useCallback(() => {
        disconnectSSE();
        stopPolling();

        setTimeout(() => {
            connectSSE();
            startPolling();
        }, 1000);
    }, [disconnectSSE, stopPolling, connectSSE, startPolling]);

    // Initialize connections
    useEffect(() => {
        if (isAuthenticated && userId && userRole) {
            connectSSE();
            // Only start polling as fallback - it will check SSE status before polling
            startPolling();
        }

        return () => {
            disconnectSSE();
            stopPolling();
        };
    }, [isAuthenticated, userId, userRole, connectSSE, startPolling, disconnectSSE, stopPolling]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            disconnectSSE();
            stopPolling();
        };
    }, [disconnectSSE, stopPolling]);

    return {
        isConnected: connectionStatus === 'connected',
        connectionStatus,
        lastUpdate,
        reconnect,
    };
};
