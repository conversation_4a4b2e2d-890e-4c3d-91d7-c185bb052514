import React, { useState } from "react";
import { toast } from "react-toastify";
import { X, FileText, Upload } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";

// Types
interface ResumeMatchModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedJob: {
    id: number;
    client: string;
    role: string;
  } | null;
}

interface MatchData {
  experience_match_percentage: number;
  experience_unmatch_percentage: number;
  skill_match_percentage: number;
  overall_match_percentage: number;
}

export function ResumeMatchModal({
  isOpen,
  onClose,
  selectedJob,
}: ResumeMatchModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [candidateExperience, setCandidateExperience] = useState("");
  const [waitForSubmission, setWaitForSubmission] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [matchData, setMatchData] = useState<MatchData>({
    experience_match_percentage: 0,
    experience_unmatch_percentage: 0,
    skill_match_percentage: 0,
    overall_match_percentage: 0,
  });

  // Reset state when modal closes
  const handleClose = () => {
    setSelectedFile(null);
    setCandidateExperience("");
    setShowResults(false);
    setMatchData({
      experience_match_percentage: 0,
      experience_unmatch_percentage: 0,
      skill_match_percentage: 0,
      overall_match_percentage: 0,
    });
    onClose();
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  // Convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 data
        const base64Data = result.split(",")[1];
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedJob) {
      toast.error("No job selected");
      return;
    }

    if (!selectedFile) {
      toast.error("Please upload a resume before submitting.");
      return;
    }

    if (waitForSubmission) return;

    setWaitForSubmission(true);

    try {
      const base64String = await fileToBase64(selectedFile);
      const body_data = {
        user_id: localStorage.getItem("user_id"),
        resume: base64String,
        job_id: selectedJob.id,
        candidate_experience: candidateExperience || undefined,
      };

      const response = await fetch(
        "http://142.93.222.128:8081/check_resume_match",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(body_data),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      setShowResults(true);
      setMatchData({
        experience_match_percentage: data.experience_match_percentage || 0,
        experience_unmatch_percentage: data.experience_unmatch_percentage || 0,
        skill_match_percentage: data.skill_match_percentage || 0,
        overall_match_percentage: data.overall_match_percentage || 0,
      });

      toast.success("Resume analysis completed!");
    } catch (error) {
      console.error("Resume match error:", error);
      toast.error("An error occurred while analyzing the resume. Please try again.");
    } finally {
      setWaitForSubmission(false);
    }
  };

  // Get progress bar color based on percentage
  const getProgressColor = (percentage: number): string => {
    if (percentage >= 75) return "bg-green-500";
    if (percentage >= 50) return "bg-yellow-500";
    return "bg-red-500";
  };

  if (!selectedJob) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Resume Match
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700"
            >
              {/* <X className="h-4 w-4" /> */}
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Job Details */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="font-medium text-gray-600">ID:</div>
              <div className="text-gray-900">{selectedJob.id}</div>
              <div className="font-medium text-gray-600">Client:</div>
              <div className="text-gray-900">{selectedJob.client}</div>
              <div className="font-medium text-gray-600">Role:</div>
              <div className="text-gray-900">{selectedJob.role}</div>
            </div>
          </div>

          {/* Upload Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="resume" className="text-sm font-medium text-gray-700">
                Resume <span className="text-red-500">*</span>
              </Label>
              <div className="mt-1">
                <Input
                  id="resume"
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileChange}
                  disabled={waitForSubmission}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
              </div>
              {selectedFile && (
                <p className="text-xs text-gray-600 mt-1">
                  Selected: {selectedFile.name}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="candidate_experience" className="text-sm font-medium text-gray-700">
                Candidate Experience (Optional)
              </Label>
              <Input
                id="candidate_experience"
                type="text"
                value={candidateExperience}
                onChange={(e) => setCandidateExperience(e.target.value)}
                disabled={waitForSubmission}
                placeholder="e.g., 5 years"
                className="mt-1"
              />
            </div>

            <Button
              type="submit"
              disabled={waitForSubmission || !selectedFile}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {waitForSubmission ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Analyzing...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  Analyze Resume
                </div>
              )}
            </Button>
          </form>

          {/* Results */}
          {showResults && (
            <div className="space-y-4 pt-4 border-t">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                Matching Results
              </h3>

              <div className="space-y-3">
                {/* Experience Match */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium text-gray-700">Experience Match</span>
                    <span className="font-semibold text-gray-900">
                      {matchData.experience_match_percentage}%
                    </span>
                  </div>
                  <Progress
                    value={matchData.experience_match_percentage}
                    className="h-2"
                  />
                </div>

                {/* Skill Match */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium text-gray-700">Skill Match</span>
                    <span className="font-semibold text-gray-900">
                      {matchData.skill_match_percentage}%
                    </span>
                  </div>
                  <Progress
                    value={matchData.skill_match_percentage}
                    className="h-2"
                  />
                </div>

                {/* Overall Match */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium text-gray-700">Overall Match</span>
                    <span className="font-semibold text-gray-900">
                      {matchData.overall_match_percentage}%
                    </span>
                  </div>
                  <Progress
                    value={matchData.overall_match_percentage}
                    className="h-2"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
