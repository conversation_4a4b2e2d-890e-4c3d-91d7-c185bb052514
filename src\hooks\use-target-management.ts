import { useState, useCallback } from 'react';
import { toast } from 'react-toastify';
import {
    ApiService,
    type RecruiterTargetsData,
    type UpdateRecruiterTargetsRequest,
    type BackendTargetData
} from '@/services/api';

export interface UseTargetManagementReturn {
    // State
    targets: RecruiterTargetsData[];
    loading: boolean;
    error: string | null;

    // Actions
    fetchTargets: () => Promise<void>;
    fetchRecruiterOwnTargets: (recruiterId: number) => Promise<RecruiterTargetsData | null>;
    updateTarget: (managerId: string, recruiterTargets: RecruiterTargetsData, actualRecruiterId?: number) => Promise<boolean>;

    // Utilities
    clearError: () => void;
    getTargetByRecruiterId: (recruiterId: string) => RecruiterTargetsData | undefined;
}

// Helper function to convert backend data to frontend format
const convertBackendToFrontend = (backendData: BackendTargetData, recruiterName?: string): RecruiterTargetsData => {
    return {
        recruiterId: backendData.recruiterId.toString(),
        recruiterName: recruiterName || `Recruiter ${backendData.recruiterId}`,
        timePeriod: 'weekly', // Always weekly
        targets: {
            profilesSubmitted: backendData.profilesSubmitted,
            interviewsScheduled: backendData.interviewsScheduled,
            offers: backendData.offers,
            joiners: backendData.joiners
        },
        lastUpdated: backendData.lastUpdated
    };
};

export function useTargetManagement(): UseTargetManagementReturn {
    const [targets, setTargets] = useState<RecruiterTargetsData[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const clearError = useCallback(() => {
        setError(null);
    }, []);

    const fetchTargets = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await ApiService.getRecruiterTargets();

            if (response.status === 'success') {
                // Convert backend data to frontend format
                const frontendTargets = response.data.map(backendTarget =>
                    convertBackendToFrontend(backendTarget)
                );
                setTargets(frontendTargets);
            } else {
                throw new Error(response.message || 'Failed to fetch targets');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch targets';
            setError(errorMessage);
            toast.error(errorMessage);
        } finally {
            setLoading(false);
        }
    }, []);

    const fetchRecruiterOwnTargets = useCallback(async (recruiterId: number): Promise<RecruiterTargetsData | null> => {
        setLoading(true);
        setError(null);

        try {
            const response = await ApiService.getRecruiterOwnTargets(recruiterId);

            if (response.status === 'success') {
                if (response.data) {
                    // Return the target data for this recruiter
                    return convertBackendToFrontend(response.data);
                } else {
                    // No targets found for this recruiter
                    return null;
                }
            } else {
                throw new Error(response.message || 'Failed to fetch recruiter targets');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch recruiter targets';
            setError(errorMessage);
            console.error('Error fetching recruiter targets:', errorMessage);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    const updateTarget = useCallback(async (managerId: string, recruiterTargets: RecruiterTargetsData, actualRecruiterId?: number): Promise<boolean> => {
        setLoading(true);
        setError(null);

        try {
            // Use the actual recruiter ID from activeUsers, fallback to hash if not available
            let recruiterId: number;

            if (actualRecruiterId) {
                recruiterId = actualRecruiterId;
            } else {
                // Fallback: Generate numeric recruiterId from recruiterName using simple hash
                const generateRecruiterIdFromName = (name: string): number => {
                    let hash = 0;
                    for (let i = 0; i < name.length; i++) {
                        const char = name.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // Convert to 32bit integer
                    }
                    return Math.abs(hash);
                };
                recruiterId = generateRecruiterIdFromName(recruiterTargets.recruiterName);
            }

            // Transform to new API payload structure
            const request: UpdateRecruiterTargetsRequest = {
                managerId: parseInt(managerId),
                recruiterId: recruiterId,
                profilesSubmitted: {
                    min: recruiterTargets.targets.profilesSubmitted.min,
                    max: recruiterTargets.targets.profilesSubmitted.max
                },
                interviewsScheduled: {
                    min: recruiterTargets.targets.interviewsScheduled.min,
                    max: recruiterTargets.targets.interviewsScheduled.max
                },
                offers: {
                    min: recruiterTargets.targets.offers.min,
                    max: recruiterTargets.targets.offers.max
                },
                joiners: {
                    min: recruiterTargets.targets.joiners.min,
                    max: recruiterTargets.targets.joiners.max
                },

            };

            console.log('Sending update targets request:', request);
            const response = await ApiService.updateRecruiterTargets(request);
            console.log('Update targets response:', response);

            if (response.status === 'success') {
                // Update local state
                setTargets(prev => {
                    const index = prev.findIndex(t => t.recruiterId === recruiterTargets.recruiterId);

                    // Convert backend response to frontend format if available
                    const updatedTarget = response.data
                        ? convertBackendToFrontend(response.data, recruiterTargets.recruiterName)
                        : recruiterTargets;

                    if (index !== -1) {
                        // Update existing
                        const newTargets = [...prev];
                        newTargets[index] = updatedTarget;
                        return newTargets;
                    } else {
                        // Add new
                        return [...prev, updatedTarget];
                    }
                });

                toast.success(`Targets updated for ${recruiterTargets.recruiterName}`);
                return true;
            } else {
                throw new Error(response.message || 'Failed to update targets');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to update targets';
            setError(errorMessage);
            toast.error(errorMessage);
            return false;
        } finally {
            setLoading(false);
        }
    }, []);



    const getTargetByRecruiterId = useCallback((recruiterId: string): RecruiterTargetsData | undefined => {
        return targets.find(t => t.recruiterId === recruiterId);
    }, [targets]);

    return {
        // State
        targets,
        loading,
        error,

        // Actions
        fetchTargets,
        fetchRecruiterOwnTargets,
        updateTarget,

        // Utilities
        clearError,
        getTargetByRecruiterId,
    };
}
