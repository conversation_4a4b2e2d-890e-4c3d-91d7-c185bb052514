import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { MessageSquare, Calendar, User } from 'lucide-react';

interface Comment {
  comment: string;
  timestamp: string;
}

interface CommentsData {
  [userName: string]: Comment[];
}

interface CommentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidateName: string;
  comments: CommentsData;
}

export const CommentsModal: React.FC<CommentsModalProps> = ({
  isOpen,
  onClose,
  candidateName,
  comments
}) => {
  // Debug: Log the comments data
  console.log('CommentsModal - comments data:', comments);
  console.log('CommentsModal - comments type:', typeof comments);
  console.log('CommentsModal - comments keys:', Object.keys(comments || {}));

  // Convert comments object to array and sort by timestamp
  const getAllComments = () => {
    const allComments: Array<Comment & { user: string }> = [];

    if (!comments || typeof comments !== 'object') {
      console.log('CommentsModal - No valid comments object found');
      return allComments;
    }

    Object.entries(comments).forEach(([user, userComments]) => {
      console.log(`CommentsModal - Processing user: ${user}, comments:`, userComments);
      if (Array.isArray(userComments)) {
        userComments.forEach(comment => {
          // Only include comments that have actual text content
          if (comment.comment && comment.comment.trim() !== '') {
            allComments.push({
              ...comment,
              user
            });
          }
        });
      }
    });

    // Sort by timestamp (newest first)
    return allComments.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return {
        date: date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        }),
        time: date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        })
      };
    } catch (error) {
      return {
        date: 'Invalid Date',
        time: ''
      };
    }
  };

  // Check if comment content exceeds the container height
  const hasScrollableContent = (commentText: string) => {
    // Rough estimate: if comment is longer than ~100 characters, it likely exceeds 2 lines
    return commentText.length > 100;
  };

  const allComments = getAllComments();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2 text-xl font-semibold">
            <MessageSquare className="h-5 w-5 text-blue-600" />
            Comments for {candidateName}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {allComments.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500">
              <MessageSquare className="h-12 w-12 mb-4 text-gray-300" />
              <p className="text-lg font-medium">No comments found</p>
              <p className="text-sm">This candidate doesn't have any comments yet.</p>
              <div className="mt-4 p-4 bg-gray-100 rounded-lg text-xs text-gray-600">
                <p><strong>Debug Info:</strong></p>
                <p>Comments data: {JSON.stringify(comments, null, 2)}</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {allComments.map((comment, index) => {
                const { date, time } = formatTimestamp(comment.timestamp);

                return (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-sm text-blue-600">
                          <User className="h-4 w-4" />
                          <span className="font-medium">{comment.user}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Calendar className="h-3 w-3" />
                        <span>{date} at {time}</span>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-md p-3 relative">
                      <div className="max-h-[2.75rem] overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full">
                        <p className="text-gray-800 leading-relaxed whitespace-pre-wrap text-sm">
                          {comment.comment}
                        </p>
                      </div>
                      {/* Fade effect to indicate scrollable content */}
                      {hasScrollableContent(comment.comment) && (
                        <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-t from-gray-50 to-transparent pointer-events-none"></div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        <div className="flex-shrink-0 border-t pt-4 mt-4">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Total comments: {allComments.length}
            </div>
            <Button
              onClick={onClose}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
