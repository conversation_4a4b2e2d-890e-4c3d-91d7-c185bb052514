import React from 'react';
import { Wifi, WifiOff, Loader2, AlertCircle } from 'lucide-react';

interface RealtimeStatusIndicatorProps {
    status: 'connected' | 'disconnected' | 'connecting' | 'error';
    lastUpdate?: Date | null;
    onReconnect?: () => void;
    className?: string;
}

export function RealtimeStatusIndicator({
    status,
    lastUpdate,
    onReconnect,
    className = ''
}: RealtimeStatusIndicatorProps) {
    const getStatusConfig = () => {
        switch (status) {
            case 'connected':
                return {
                    icon: Wifi,
                    color: 'text-green-500',
                    bgColor: 'bg-green-100',
                    text: 'Live',
                    description: 'Real-time updates active'
                };
            case 'connecting':
                return {
                    icon: Loader2,
                    color: 'text-yellow-500',
                    bgColor: 'bg-yellow-100',
                    text: 'Connecting',
                    description: 'Establishing connection...'
                };
            case 'error':
                return {
                    icon: AlertCircle,
                    color: 'text-red-500',
                    bgColor: 'bg-red-100',
                    text: 'Error',
                    description: 'Connection failed'
                };
            default:
                return {
                    icon: WifiOff,
                    color: 'text-gray-500',
                    bgColor: 'bg-gray-100',
                    text: 'Offline',
                    description: 'No real-time updates'
                };
        }
    };

    const config = getStatusConfig();
    const Icon = config.icon;

    const formatLastUpdate = (date: Date | null) => {
        if (!date) return 'Never';

        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);

        if (seconds < 60) return `${seconds}s ago`;
        if (minutes < 60) return `${minutes}m ago`;
        return date.toLocaleTimeString();
    };

    return (
        <div className={`flex items-center space-x-2 ${className}`}>
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full ${config.bgColor}`}>
                <Icon
                    className={`h-3 w-3 ${config.color} ${status === 'connecting' ? 'animate-spin' : ''}`}
                />
                <span className={`text-xs font-medium ${config.color}`}>
                    {config.text}
                </span>
            </div>

            {lastUpdate && status === 'connected' && (
                <span className="text-xs text-gray-500">
                    Updated {formatLastUpdate(lastUpdate)}
                </span>
            )}

            {status === 'error' && onReconnect && (
                <button
                    onClick={onReconnect}
                    className="text-xs text-blue-500 hover:text-blue-700 underline"
                >
                    Retry
                </button>
            )}

            <div className="group relative">
                <div className="w-2 h-2 bg-gray-300 rounded-full cursor-help"></div>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
                    {config.description}
                </div>
            </div>
        </div>
    );
}

