import { useState } from "react";


interface LogoutConfirmationDialogProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export function LogoutConfirmationDialog({
  isOpen,
  onConfirm,
  onCancel,
}: LogoutConfirmationDialogProps) {
  const [selectedOption, setSelectedOption] = useState<string>("");

  if (!isOpen) return null;

  const handleConfirm = () => {
    if (selectedOption === "updated") {
      // User confirmed they have updated all statuses
      onConfirm();
    } else if (selectedOption === "willUpdate") {
      // User wants to stay logged in to update statuses
      onCancel();
    } else {
      // User wants to logout anyway
      onConfirm();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-white/60 backdrop-saturate-100 flex items-center justify-center z-[9999] p-4 transition-all duration-300 ease-in-out"
      onClick={onCancel}
    >
      {/* Dialog */}
      <div
        className="relative bg-white rounded-lg mt-30 shadow-xl max-w-lg w-full mx-4 p-6 transition-all duration-300 ease-in-out"
        onClick={(e) => e.stopPropagation()}
      >

        {/* Success Message */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          {selectedOption === "updated" ? (
            <p className="text-green-800 text-center text-sm font-medium">
              👏 Fantastic! Thanks for keeping everything up to date.
            </p>
          ) : (
            <p className="text-green-800 text-center text-sm font-medium">
              Awesome work today! 🎉 Before you log out, please make sure all your candidates are uploaded and statuses are updated for the day.
            </p>
          )}
        </div>

        {/* Radio Button Options */}
        <div className="space-y-3 mb-6">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="statusUpdate"
              value="updated"
              checked={selectedOption === "updated"}
              onChange={(e) => setSelectedOption(e.target.value)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
            />
            <span className="text-sm text-gray-700">
              ✅ Yes, all done
            </span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="statusUpdate"
              value="willUpdate"
              checked={selectedOption === "willUpdate"}
              onChange={(e) => setSelectedOption(e.target.value)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
            />
            <span className="text-sm text-gray-700">
              ⏳ Not yet, I'll update now
            </span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="statusUpdate"
              value="logoutAnyway"
              checked={selectedOption === "logoutAnyway"}
              onChange={(e) => setSelectedOption(e.target.value)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
            />
            <span className="text-sm text-gray-700">
              🔔 Logout me
            </span>
          </label>
        </div>

        {/* Buttons */}
        <div className="flex gap-3">
          <button
            onClick={handleConfirm}
            disabled={!selectedOption}
            className="flex-1 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-lg font-medium transition-colors"
          >
            {selectedOption === "updated" ? "Logout" :
              selectedOption === "willUpdate" ? "Stay & Update" :
                "Logout"}
          </button>
          <button
            onClick={onCancel}
            className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
