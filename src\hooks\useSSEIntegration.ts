import { useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { sseService, type SSEEvent } from '@/services/sse';
import { toast } from 'react-hot-toast';

interface UseSSEIntegrationProps {
  userId: string;
  userRole: string;
  onCandidateUpdate?: (event: SSEEvent) => void;
  onJobUpdate?: (event: SSEEvent) => void;
  onProfileUpdate?: (event: SSEEvent) => void;
  showNotifications?: boolean;
}

/**
 * Custom hook for integrating SSE functionality into components
 * Handles automatic connection, event listening, and cleanup
 */
export const useSSEIntegration = ({
  userId,
  userRole,
  onCandidateUpdate,
  onJobUpdate,
  onProfileUpdate,
  showNotifications = true
}: UseSSEIntegrationProps) => {
  const dispatch = useDispatch();

  const handleSSEEvent = useCallback((event: SSEEvent) => {
    // Handle different event types
    switch (event.type) {
      case 'candidate_update':
      case 'candidate_data_update':
        if (onCandidateUpdate) {
          onCandidateUpdate(event);
        }
        
        if (showNotifications) {
          const isDataUpdate = event.type === 'candidate_data_update';
          const candidateId = event.data.candidateId;
          const isNew = event.data.isNew;
          
          if (isDataUpdate && isNew) {
            toast.success(`New candidate added: ID ${candidateId}`);
          } else if (isDataUpdate) {
            toast.info(`Candidate data updated: ID ${candidateId}`);
          } else {
            toast.info(`Candidate status updated: ID ${candidateId}`);
          }
        }
        break;

      case 'job_update':
      case 'job_data_update':
        if (onJobUpdate) {
          onJobUpdate(event);
        }
        
        if (showNotifications) {
          const isDataUpdate = event.type === 'job_data_update';
          const jobId = event.data.jobId;
          const isNew = event.data.isNew;
          
          if (isDataUpdate && isNew) {
            toast.success(`New job created: ID ${jobId}`);
          } else if (isDataUpdate) {
            toast.info(`Job data updated: ID ${jobId}`);
          } else {
            toast.info(`Job status updated: ID ${jobId}`);
          }
        }
        break;

      case 'profile_update':
      case 'profile_data_update':
        if (onProfileUpdate) {
          onProfileUpdate(event);
        }
        
        if (showNotifications) {
          const isDataUpdate = event.type === 'profile_data_update';
          const profileId = event.data.profileId;
          const isNew = event.data.isNew;
          
          if (isDataUpdate && isNew) {
            toast.success(`New profile assigned: ID ${profileId}`);
          } else if (isDataUpdate) {
            toast.info(`Profile data updated: ID ${profileId}`);
          } else {
            toast.info(`Profile status updated: ID ${profileId}`);
          }
        }
        break;

      case 'system_message':
        if (showNotifications && event.data.message) {
          toast(event.data.message, {
            icon: '📢',
            duration: 5000,
          });
        }
        break;

      default:
        console.log('Unhandled SSE event type:', event.type);
    }
  }, [onCandidateUpdate, onJobUpdate, onProfileUpdate, showNotifications]);

  useEffect(() => {
    // Connect to SSE
    sseService.connect(userId, userRole);

    // Add event listener
    sseService.addEventListener(handleSSEEvent);

    // Cleanup function
    return () => {
      sseService.removeEventListener(handleSSEEvent);
      // Note: We don't disconnect here as other components might be using SSE
      // The service handles connection management internally
    };
  }, [userId, userRole, handleSSEEvent]);

  // Return utility functions
  return {
    isConnected: () => sseService.getConnectionStatus(),
    disconnect: () => sseService.disconnect(),
    reconnect: () => sseService.connect(userId, userRole),
  };
};

/**
 * Simplified hook for basic SSE integration without custom handlers
 */
export const useBasicSSE = (userId: string, userRole: string, showNotifications = true) => {
  return useSSEIntegration({
    userId,
    userRole,
    showNotifications,
  });
};

export default useSSEIntegration;
