import { useState } from "react";
import { toast } from "react-toastify";
import { Trash2, Alert<PERSON>riangle } from "lucide-react";
import { Job } from "@/store/slices/jobsSlice";
import { Candidate } from "@/types/candidate";
import { ApiService } from "@/services/api";
import { useAppDispatch } from "@/store/hooks";
import { removeJob } from "@/store/slices/jobsSlice";
import { removeCandidate } from "@/store/slices/candidatesSlice";

interface JobDeletionModalProps {
    isOpen: boolean;
    onClose: () => void;
    job: Job | null;
    associatedCandidates: Candidate[];
    userName?: string;
    userEmail?: string;
}

export function JobDeletionModal({
    isOpen,
    onClose,
    job,
    associatedCandidates,
}: JobDeletionModalProps) {
    const [isDeleting, setIsDeleting] = useState(false);
    const dispatch = useAppDispatch();

    if (!isOpen || !job) return null;

    const candidateCount = associatedCandidates.length;
    const hasCandidates = candidateCount > 0;

    const handleDelete = async () => {
        setIsDeleting(true);
        try {
            const response = await ApiService.deleteJob(job.id);
            if (response.status === 'success') {
                toast.success(response.message);

                // Update Redux state locally for immediate UI update
                dispatch(removeJob(job.id));

                // Remove all associated candidates from Redux state
                associatedCandidates.forEach(candidate => {
                    dispatch(removeCandidate(candidate.id));
                });

                onClose();
            } else {
                toast.error(`Failed to delete job: ${response.message}`);
            }
        } catch (error) {
            toast.error("Failed to delete job");
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Backdrop */}
            <div
                className="absolute inset-0 bg-transparent"
                onClick={onClose}
            />

            {/* Modal */}
            <div className="relative bg-white rounded-lg shadow-xl max-w-sm w-full mx-4 border border-gray-200">
                <div className="p-6">
                    {hasCandidates ? (
                        <div className="text-center">
                            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Cannot Delete Job
                            </h3>
                            <p className="text-sm text-gray-600 mb-4">
                                This job has <strong>{candidateCount} candidate(s)</strong> associated with it.
                            </p>
                            <p className="text-sm text-gray-500">
                                Please remove or reassign all candidates before deleting this job.
                            </p>
                        </div>
                    ) : (
                        <div className="text-center">
                            <Trash2 className="h-12 w-12 text-red-500 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Delete Job
                            </h3>
                            <p className="text-sm text-gray-600 mb-2">
                                <strong>{job.role}</strong> for <strong>{job.client}</strong>
                            </p>
                            <p className="text-sm text-gray-500">
                                This action cannot be undone.
                            </p>
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-end gap-3 p-2 border-t border-gray-200">
                    {hasCandidates ? (
                        <button
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center gap-2"
                        >
                            OK
                        </button>
                    ) : (
                        <>
                            <button
                                onClick={onClose}
                                disabled={isDeleting}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleDelete}
                                disabled={isDeleting}
                                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center gap-2"
                            >
                                {isDeleting ? (
                                    <>
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                        Deleting...
                                    </>
                                ) : (
                                    <>
                                        <Trash2 className="h-4 w-4" />
                                        Delete Job
                                    </>
                                )}
                            </button>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}
