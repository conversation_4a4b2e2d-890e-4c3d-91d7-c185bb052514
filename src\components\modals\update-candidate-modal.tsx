import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { useAppDispatch } from "@/store/hooks";
import { updateCandidateStatus } from "@/store/slices/candidatesSlice";
import { X, ChevronDown } from "lucide-react";
import { type Candidate } from "@/types/candidate";
import { ApiService, type UpdateCandidateRequest, type ActiveUser } from "@/services/api";
import { useUser } from "@/contexts/user-context";

interface UpdateCandidateModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidate: Candidate | null;
  onUpdate: (candidateId: string, status: string, comment: string) => void;
}

const STATUS_OPTIONS = [
  "Peer Review",
  "Internal Screening – In Progress",
  "Internal Screening – Rejected",
  "Internal Interview – In Progress",
  "Internal Interview – Rejected",
  "Client Screening – In Progress",
  "Client Screening – Rejected",
  "Client – Duplicate Profile",
  "Client Interview – In Progress",
  "Client Interview – Rejected",
  "Client Interview – HR Round",
  "Client Interview – Managerial Round",
  "Interview Reschedule - Client",
  "Interview Reschedule - Candidate",
  "Interview No-show",
  "Offer – Released",
  "Offer – Declined by Candidate",
  "Offered – Yet to Join",
  "Onboarded",
  "Position – On Hold",
  "Profile – On Hold",
  "Candidate – Dropped",
];

export function UpdateCandidateModal({
  isOpen,
  onClose,
  candidate,
  onUpdate,
}: UpdateCandidateModalProps) {
  const { userId, userName } = useUser();
  const dispatch = useAppDispatch();
  const [selectedStatus, setSelectedStatus] = useState("");
  const [comment, setComment] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [priority, setPriority] = useState(false); // Changed to boolean for checkbox
  const [peerReviewer, setPeerReviewer] = useState('');
  const [peerReviewerEmail, setPeerReviewerEmail] = useState('');
   const [peerReviewerId, setPeerReviewerId] = useState('');
  // const [peerReviewerId, setPeerReviewerId] = useState(''); // Commented out unused variable
  const [peerReviewers, setPeerReviewers] = useState<ActiveUser[]>([]);
  const [isPeerReviewerDropdownOpen, setIsPeerReviewerDropdownOpen] = useState(false);

  // Reset form when candidate changes
  useEffect(() => {
    if (candidate && isOpen) {
      setSelectedStatus(candidate.status || "");
      setComment("");
      setPriority(false);
      setPeerReviewer('');
      setPeerReviewerEmail('');
      setPeerReviewerId('');
      // setPeerReviewerId(''); // Commented out unused variable
    }
  }, [candidate, isOpen]);

  // Fetch peer reviewers when modal opens
  useEffect(() => {
    const fetchPeerReviewers = async () => {
      try {
        const response = await ApiService.fetchActiveUsers();
        const allUsers = [...response.active_users_manager, ...response.active_users_recruiter];
        const peerReviewers = allUsers.filter(user => user.peer_reviewer_status === true);
        setPeerReviewers(peerReviewers);
      } catch (error) {
        console.error('Failed to fetch peer reviewers:', error);
        toast.error('Failed to fetch peer reviewers');
      }
    };

    if (isOpen) {
      fetchPeerReviewers();
    }
  }, [isOpen]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.peer-reviewer-dropdown')) {
        setIsPeerReviewerDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (!isOpen || !candidate) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedStatus || !userId) {
      return;
    }

    setLoading(true);

    try {
       const updateData: UpdateCandidateRequest = {
        user_id: Number(userId), 
        candidate_status: selectedStatus === "Peer Review" ? "PR-Pending" : selectedStatus,
        commentupdate: { [userName || 'user']: comment },
        priority: priority ? 'High' : 'Low', // Convert boolean to string
        peer_reviewer: peerReviewer || null,
        peer_reviewer_email: peerReviewerEmail || null,
        peer_reviewer_user_id: peerReviewerId || null,
      };
      const response = await ApiService.updateCandidate(candidate.id, updateData);

      if (response.status === 'success') {
        // Update Redux locally without re-fetch
        dispatch(updateCandidateStatus({ id: candidate.id, status: selectedStatus, comment }));
        onUpdate(candidate.id.toString(), selectedStatus, comment);
        toast.success('Candidate status updated successfully!');
        onClose();
        setComment("");
        setPriority(false);
        setPeerReviewer('');
        setPeerReviewerEmail('');
        setPeerReviewerId(''); 
      } else {
        throw new Error(response.message || 'Failed to update candidate');
      }
    } catch (error) {
      // Error updating candidate
      toast.error('Failed to update candidate');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusSelect = (status: string) => {
    setSelectedStatus(status);
    setIsDropdownOpen(false);

    // Reset peer reviewer fields when status changes from Peer Review
    if (status !== "Peer Review") {
      setPeerReviewer('');
      setPeerReviewerEmail('');
      setPeerReviewerId(''); // Commented out unused variable
      setPriority(false);
    }
  };

  const getAvailableStatuses = () => {
    // Filter statuses based on current status (similar to your logic)
    if (
      candidate.status === "PR-Rejected" ||
      candidate.status === "SCREENING" ||
      candidate.status === "Internal Screening" ||
      candidate.status === "Internal Screening – In Progress"
    ) {
      return STATUS_OPTIONS;
    }
    return STATUS_OPTIONS.filter((status) => status !== "Peer Review");
  };

  return (
    <div className="fixed inset-0 bg-white/40 backdrop-saturate-150 flex items-center justify-center z-50 p-4 transition-all">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center bg-blue-600 text-white justify-between p-3">
          <h2 className="text-xl text-white font-semibold">Update Candidate Status</h2>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
            disabled={loading}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-3">
          {/* Candidate Info */}
          <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <div className="text-sm text-gray-900 font-semibold">
                {candidate.name}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mobile
              </label>
              <div className="text-sm text-gray-900">{candidate.phone}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <div className="text-sm text-gray-900">{candidate.email}</div>
            </div>
          </div>



          {/* Current Status */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Candidate Current Status
            </label>
            <div className="relative">
              <button
                type="button"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="w-full px-4 py-3 text-left bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 flex items-center justify-between"
                disabled={loading}
              >
                <span className="text-sm">
                  Level: {selectedStatus || "Select Status"}
                </span>
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${isDropdownOpen ? "rotate-180" : ""
                    }`}
                />
              </button>

              {isDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {getAvailableStatuses().map((status) => (
                    <button
                      key={status}
                      type="button"
                      onClick={() => handleStatusSelect(status)}
                      className="w-full px-4 py-2 text-left text-sm hover:bg-blue-50 hover:text-blue-700 transition-colors border-b border-gray-100 last:border-b-0"
                    >
                      {status}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
          {/* Priority Selection - Only for Peer Review */}
          {selectedStatus === "Peer Review" && (
            <div className="mb-6">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={priority}
                  onChange={(e) => setPriority(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={loading}
                />
                <span className="text-sm font-medium text-gray-700">
                  High Priority
                </span>
              </label>
            </div>
          )}

          {/* Peer Reviewer Section */}
          {selectedStatus === "Peer Review" && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Peer Reviewer Name
                </label>
                <div className="relative peer-reviewer-dropdown">
                  <button
                    type="button"
                    onClick={() => setIsPeerReviewerDropdownOpen(!isPeerReviewerDropdownOpen)}
                    className="w-full px-4 py-3 text-left bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 flex items-center justify-between"
                    disabled={loading}
                  >
                    <span className="text-sm">
                      {peerReviewer || "Select Peer Reviewer"}
                    </span>
                    <ChevronDown
                      className={`h-4 w-4 transition-transform ${isPeerReviewerDropdownOpen ? "rotate-180" : ""}`}
                    />
                  </button>

                  {isPeerReviewerDropdownOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      {/* Managers */}
                      {peerReviewers.filter(user => user.user_type === 'management').length > 0 && (
                        <div className="px-3 py-2 text-xs font-semibold text-white bg-blue-600 border-b border-blue-700">
                          Managers
                        </div>
                      )}
                      {peerReviewers
                        .filter(user => user.user_type === 'management')
                        .map((user) => (
                          <button
                            key={user.id}
                            type="button"
                            onClick={() => {
                              setPeerReviewer(user.username);
                              setPeerReviewerEmail(user.email);
                              setPeerReviewerId(String(user.id)); // Commented out unused variable
                              setIsPeerReviewerDropdownOpen(false);
                            }}
                            className="w-full px-3 py-2 text-left text-sm hover:bg-blue-50 hover:text-blue-700 transition-colors border-b border-gray-100 last:border-b-0"
                          >
                            {user.username}
                          </button>
                        ))}

                      {/* Recruiters */}
                      {peerReviewers.filter(user => user.user_type !== 'management').length > 0 && (
                        <div className="px-3 py-2 text-xs font-semibold text-white bg-green-600 border-b border-green-700">
                          Recruiters
                        </div>
                      )}
                      {peerReviewers
                        .filter(user => user.user_type !== 'management')
                        .map((user) => (
                          <button
                            key={user.id}
                            type="button"
                            onClick={() => {
                              setPeerReviewer(user.username);
                              setPeerReviewerEmail(user.email);
                              setPeerReviewerId(String(user.id)); // Commented out unused variable
                              setIsPeerReviewerDropdownOpen(false);
                            }}
                            className="w-full px-3 py-2 text-left text-sm hover:bg-blue-50 hover:text-blue-700 transition-colors border-b border-green-100 last:border-b-0"
                          >
                            {user.username}
                          </button>
                        ))}
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comment
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="Add your comment here..."
              disabled={loading}
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              disabled={loading}
            >
              Close
            </button>
            <button
              type="submit"
              disabled={!selectedStatus || loading}
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? "Updating..." : "Update Candidate"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
