
import {
  User<PERSON><PERSON><PERSON>,
  Target,
  Calendar,
  Users,
  Clock,
  FileText,
  Users2,
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowR<PERSON>,
  TrendingUp,
  Check,
  ChevronDown
} from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { RecruiterTargets } from "../modals/target-management-modal";
import { useUser } from "@/contexts/user-context";
import { useDataManagement } from "@/hooks/use-data-management";
import { useTargetManagement } from "@/hooks/use-target-management";
import { type Candidate } from "@/types/candidate";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectJobs } from "@/store/selectors/jobsSelectors";
import { fetchJobs } from "@/store/slices/jobsSlice";
import { ViewAllPipelineCandidatesModal } from "../modals/view-all-pipeline-candidates-modal";
// KPI Card Component
interface KPICardProps {
  title: string;
  currentValue: number;
  targetRange?: string; // Make targetRange optional
  color: string;
  icon: React.ComponentType<any>;
}

function KPICard({
  title,
  currentValue,
  targetRange,
  color,
  icon: Icon,
}: KPICardProps) {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <Icon className={`w-5 h-5 ${color}`} />
      </div>
      <div className="text-3xl font-bold text-gray-900 mb-1">
        {currentValue}
      </div>
      {targetRange && (
        <div className="text-sm text-gray-500">Target: {targetRange}</div>
      )}
    </div>
  );
}

// Pipeline Stage Component
interface PipelineStageProps {
  title: string;
  candidates: string[];
  count: number;
  color: string;
  hasMore?: boolean;
}

function PipelineStage({
  title,
  candidates,
  count,
  color,
  hasMore = false,
}: PipelineStageProps) {
  return (
    <div className="pipeline-column px-1 py-1 bg-white rounded-2xl shadow-md flex-shrink-0 w-[115px] mr-4">
      <h2 className="text-lg font-semibold text-gray-700 flex items-center justify-between mb-1 border-b pb-1">
        <span className="capitalize">{title}</span>
        <span className={`px-2 py-1 text-xs font-semibold rounded-full text-white ${color}`}>
          {count}
        </span>
      </h2>
      <div className="space-y-4 max-h-[160px] overflow-y-auto pr-1 ">
        {candidates && candidates.length > 0 ? (
          <>
            {candidates.map((candidate, index) => (
              <div key={index} className=" p-1 ">
                <p className="text-sm font-medium text-gray-800">{candidate}</p>
              </div>
            ))}
            {hasMore && (
              <div className="p-1">
                <p className="text-xs text-gray-500 italic text-center">
                  +{count - candidates.length} more
                </p>
              </div>
            )}
          </>
        ) : (
          <p className="text-sm text-gray-400 text-center py-4">No candidates in this stage.</p>
        )}
      </div>
    </div>
  );
}

// To-Do Item Component
interface TodoItemProps {
  text: string;
  icon: React.ComponentType<any>;
  iconColor: string;
}

function TodoItem({ text, icon: Icon, iconColor }: TodoItemProps) {
  return (
    <div className="flex items-center space-x-3 p-3 bg-white rounded-lg shadow-sm border border-gray-200">
      <Icon className={`w-5 h-5 ${iconColor}`} />
      <span className="text-sm text-gray-700">{text}</span>
    </div>
  );
}

export function RecruiterDashboard() {
  const { userName, userId } = useUser();
  const dispatch = useAppDispatch();

  // Get current user info from localStorage
  const currentUserId = localStorage.getItem('userId');

  // Use data management hook for Redux store access
  const {
    candidates,
    candidatesLoading: loading,
    candidatesError: error,
    refreshCandidates
  } = useDataManagement();

  // Use target management hook for API calls
  const { fetchRecruiterOwnTargets } = useTargetManagement();

  // Get jobs data from Redux store
  const jobs = useAppSelector(selectJobs);

  // Get user's full name from localStorage (stored during login)
  const userFullName = localStorage.getItem('name') || '';

  // Get targets from API
  const [targets, setTargets] = useState<RecruiterTargets | null>(null);

  // Modal state for viewing all pipeline candidates
  const [isViewAllModalOpen, setIsViewAllModalOpen] = useState(false);
  const [selectedPipelineStage, setSelectedPipelineStage] = useState<string>("");
  const [selectedPipelineCandidates, setSelectedPipelineCandidates] = useState<Candidate[]>([]);

  // Date filter state
  const [dateFilter, setDateFilter] = useState<number | null>(null);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState<string>("");
  const [customEndDate, setCustomEndDate] = useState<string>("");
  const [appliedCustomDateRange, setAppliedCustomDateRange] = useState<{
    start: string;
    end: string;
  } | null>(null);

  // Refs for date dropdown
  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const dateButtonRef = useRef<HTMLButtonElement>(null);

  // Fetch candidates data on component mount using Redux
  useEffect(() => {
    if (userName && currentUserId) {
      refreshCandidates();
      dispatch(fetchJobs({ username: userName }));
    }
  }, [userName, currentUserId, refreshCandidates, dispatch]);

  useEffect(() => {
    // Load targets from API
    const loadTargets = async () => {
      if (userId) {
        try {
          const recruiterTargets = await fetchRecruiterOwnTargets(parseInt(userId));
          if (recruiterTargets) {
            // Convert from RecruiterTargetsData to RecruiterTargets format
            const formattedTargets: RecruiterTargets = {
              recruiterId: recruiterTargets.recruiterId,
              recruiterName: recruiterTargets.recruiterName,
              timePeriod: recruiterTargets.timePeriod,
              targets: recruiterTargets.targets,
              lastUpdated: recruiterTargets.lastUpdated,
            };
            setTargets(formattedTargets);
          } else {
            // No targets found, set to null
            setTargets(null);
          }
        } catch (error) {
          console.error('Failed to load recruiter targets:', error);
          setTargets(null);
        }
      }
    };

    loadTargets();
  }, [userId, fetchRecruiterOwnTargets]);



  // Get current targets
  const currentTargets = targets;

  // Define the date filter options
  const dateFilterOptions = [
    { value: null, label: "All Time" },
    { value: 1, label: "Last 24 Hours" },
    { value: 7, label: "Last 7 Days" },
    { value: 30, label: "Last 30 Days" },
  ];

  // Add effect to close date dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dateDropdownRef.current &&
        !dateDropdownRef.current.contains(event.target as Node) &&
        dateButtonRef.current &&
        !dateButtonRef.current.contains(event.target as Node)
      ) {
        setIsDateDropdownOpen(false);
      }
    }

    if (isDateDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDateDropdownOpen]);

  const handleDateFilter = (days: number | null) => {
    setDateFilter(days);
    setIsDateDropdownOpen(false);
    setCustomStartDate("");
    setCustomEndDate("");
    setAppliedCustomDateRange(null);
  };

  // Get the current date filter label for display
  const getCurrentDateFilterLabel = () => {
    if (appliedCustomDateRange) {
      const startDate = new Date(
        appliedCustomDateRange.start
      ).toLocaleDateString();
      const endDate = new Date(appliedCustomDateRange.end).toLocaleDateString();
      return `${startDate} - ${endDate}`;
    }
    const option = dateFilterOptions.find((opt) => opt.value === dateFilter);
    return option ? option.label : "Select Date";
  };

  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      setAppliedCustomDateRange({
        start: customStartDate,
        end: customEndDate,
      });
      setDateFilter(null);
      setIsDateDropdownOpen(false);
    } else {
      alert("Please select both start and end dates");
    }
  };

  // Filter candidates based on selected date range
  const getFilteredCandidates = () => {
    if (!dateFilter && !appliedCustomDateRange) {
      return candidates;
    }

    const now = new Date();
    let startDate: Date;

    if (dateFilter) {
      // Calculate start date based on filter
      startDate = new Date(now.getTime() - (dateFilter * 24 * 60 * 60 * 1000));
    } else if (appliedCustomDateRange) {
      // Use custom date range
      startDate = new Date(appliedCustomDateRange.start);
      const endDate = new Date(appliedCustomDateRange.end);

      return candidates.filter(candidate => {
        const candidateDate = new Date(candidate.appliedDate);
        return candidateDate >= startDate && candidateDate <= endDate;
      });
    }

    // Filter by date filter
    return candidates.filter(candidate => {
      const candidateDate = new Date(candidate.appliedDate);
      return candidateDate >= startDate;
    });
  };

  // Get filtered candidates based on date selection
  const filteredCandidates = getFilteredCandidates();

  // Calculate real metrics from candidate data
  const calculateMetrics = (candidates: Candidate[], jobs: any[]) => {
    const metrics = {
      profilesSubmitted: 0,
      interviewsScheduled: 0,
      offers: 0,
      joiners: 0,
      requirementsAssigned: 0,
    };

    candidates.forEach(candidate => {
      const status = candidate.status.toLowerCase();

      // Count profiles submitted (candidates with any status)
      metrics.profilesSubmitted++;

      // Count interviews scheduled
      if (status.includes('interview') || status.includes('scheduled') || status.includes('shortlisted')) {
        metrics.interviewsScheduled++;
      }

      // Count offers
      if (status.includes('offer') || status.includes('placed') || status.includes('selected')) {
        metrics.offers++;
      }

      // Count joiners (candidates who joined)
      if (status.includes('joined') || status.includes('onboarded') || status.includes('hired')) {
        metrics.joiners++;
      }
    });

    // Calculate active requirements/jobs assigned to this recruiter
    const activeJobs = jobs.filter(job => {
      // Check if job is active
      if (job.job_status !== "Active") return false;

      // Handle multiple recruiters separated by commas
      const assignedRecruiters = job.recruiter.split(',').map((r: string) => r.trim());

      // We need to match either by username or by name
      const isAssignedToUser = assignedRecruiters.some((recruiter: string) => {
        // Try to match by username first
        if (recruiter === userName) return true;

        // Try to match by full name from localStorage
        if (recruiter === userFullName) return true;

        return false;
      });

      return isAssignedToUser;
    });

    metrics.requirementsAssigned = activeJobs.length;

    return metrics;
  };

  const dashboardData = calculateMetrics(filteredCandidates, jobs);

  // Calculate pipeline data from real candidate data
  const calculatePipelineData = (candidates: Candidate[]) => {
    const pipeline = {
      sourced: { count: 0, candidates: [] as string[], hasMore: false },
      screened: { count: 0, candidates: [] as string[], hasMore: false },
      submitted: { count: 0, candidates: [] as string[], hasMore: false },
      interview: { count: 0, candidates: [] as string[], hasMore: false },
      offer: { count: 0, candidates: [] as string[], hasMore: false },
      joined: { count: 0, candidates: [] as string[], hasMore: false },
    };

    // First pass: collect all candidates by stage
    const stageCollectors = {
      sourced: [] as string[],
      screened: [] as string[],
      submitted: [] as string[],
      interview: [] as string[],
      offer: [] as string[],
      joined: [] as string[],
    };

    candidates.forEach(candidate => {
      const status = candidate.status.toLowerCase();
      const candidateName = candidate.name || `Candidate ${candidate.id}`;

      if (status.includes('sourced') || status.includes('initial') || status.includes('new')) {
        stageCollectors.sourced.push(candidateName);
      } else if (status.includes('screened') || status.includes('shortlisted') || status.includes('pending')) {
        stageCollectors.screened.push(candidateName);
      } else if (status.includes('submitted') || status.includes('profile') || status.includes('active')) {
        stageCollectors.submitted.push(candidateName);
      } else if (status.includes('interview') || status.includes('scheduled') || status.includes('shortlisted')) {
        stageCollectors.interview.push(candidateName);
      } else if (status.includes('offer') || status.includes('placed') || status.includes('selected')) {
        stageCollectors.offer.push(candidateName);
      } else if (status.includes('joined') || status.includes('onboarded') || status.includes('hired')) {
        stageCollectors.joined.push(candidateName);
      }
    });

    // Second pass: apply smart display logic
    Object.keys(stageCollectors).forEach(stage => {
      const stageKey = stage as keyof typeof stageCollectors;
      const allCandidates = stageCollectors[stageKey];
      const displayLimit = allCandidates.length <= 5 ? allCandidates.length : 3;

      pipeline[stageKey].count = allCandidates.length;
      pipeline[stageKey].candidates = allCandidates.slice(0, displayLimit);
      pipeline[stageKey].hasMore = allCandidates.length > displayLimit;
    });

    return pipeline;
  };

  const pipelineData = calculatePipelineData(filteredCandidates);

  // Generate dynamic todo items based on active candidates
  const generateTodoItems = () => {
    const todoItems = [];

    // Get candidates that are in ongoing forward process (active statuses) - EXCLUDE rejected ones
    const activeCandidates = filteredCandidates.filter(candidate => {
      const status = candidate.status?.toLowerCase() || '';

      // First, exclude any rejected candidates
      if (status.includes('rejected') || status.includes('reject') || status.includes('drop')) {
        return false;
      }

      // Then include only active, forward-moving candidates
      return (
        status.includes('screening') ||
        status.includes('interview') ||
        status.includes('progress') ||
        status.includes('peer review') ||
        status.includes('offer') ||
        status.includes('pending') ||
        (status.includes('new') && !status.includes('rejected'))
      );
    });

    // Add todo items based on active candidates - limit to top 3
    activeCandidates.slice(0, 3).forEach((candidate) => {
      const fullName = `${candidate.name}`;
      const status = candidate.status;

      let todoText = '';
      let icon = Clock;
      let color = 'text-blue-500';

      if (status?.toLowerCase().includes('screening')) {
        todoText = `Follow up on ${fullName} - ${status}`;
        icon = FileText;
        color = 'text-purple-500';
      } else if (status?.toLowerCase().includes('interview')) {
        todoText = `Schedule/Follow up ${fullName} - ${status}`;
        icon = Users2;
        color = 'text-indigo-500';
      } else if (status?.toLowerCase().includes('offer')) {
        todoText = `Track ${fullName} - ${status}`;
        icon = CheckCircle;
        color = 'text-green-500';
      } else if (status?.toLowerCase().includes('peer review')) {
        todoText = `Review ${fullName} - ${status}`;
        icon = UserCheck;
        color = 'text-violet-500';
      } else if (status?.toLowerCase().includes('pending')) {
        todoText = `Process ${fullName} - ${status}`;
        icon = Clock;
        color = 'text-amber-500';
      } else {
        todoText = `Follow up ${fullName} - ${status}`;
        icon = Clock;
        color = 'text-blue-500';
      }

      todoItems.push({ text: todoText, icon, color });
    });

    // If no active candidates, show default items
    if (todoItems.length === 0) {
      todoItems.push(
        { text: "No active candidates to follow up", icon: Check, color: "text-green-500" },
        { text: "All candidates are up to date", icon: CheckCircle, color: "text-green-500" }
      );
    }

    return todoItems;
  };

  const todoItems = generateTodoItems();

  // Handle viewing all candidates across all pipeline stages
  const handleViewAllPipeline = () => {
    setSelectedPipelineStage("All Pipeline Stages");
    setSelectedPipelineCandidates(filteredCandidates);
    setIsViewAllModalOpen(true);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading dashboard data...</p>
              <p className="text-sm text-gray-500 mt-2">Fetching candidates from Redux store</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading dashboard data</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Recruiter Dashboard</h1>

            {/* Date Filter */}
            <div className="relative">
              <button
                ref={dateButtonRef}
                onClick={() => setIsDateDropdownOpen(!isDateDropdownOpen)}
                className="bg-white border border-gray-300 text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
              >
                <Calendar className="h-4 w-4 text-gray-500" />
                <span>{getCurrentDateFilterLabel()}</span>
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${isDateDropdownOpen ? "rotate-180" : ""}`}
                />
              </button>

              {isDateDropdownOpen && (
                <div
                  ref={dateDropdownRef}
                  className="absolute right-0 top-full mt-1 z-[9999] bg-white rounded-lg shadow-xl border border-gray-200 min-w-[280px] py-2 animate-in fade-in-0 zoom-in-95 duration-200"
                >
                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200">
                    Filter by Date
                  </div>
                  {dateFilterOptions.map((option) => (
                    <button
                      key={option.value || "all"}
                      onClick={() => handleDateFilter(option.value)}
                      className={`w-full text-left px-3 py-2 hover:bg-gray-50 text-sm transition-colors duration-150 flex items-center justify-between ${dateFilter === option.value
                        ? "bg-blue-50 text-blue-700 border-r-2 border-r-blue-500"
                        : "text-gray-700"
                        }`}
                    >
                      <span>{option.label}</span>
                      {dateFilter === option.value && (
                        <Check className="h-4 w-4 text-blue-600" />
                      )}
                    </button>
                  ))}

                  {/* Custom Date Range Section */}
                  <div className="border-t border-gray-200 p-3 bg-gray-50">
                    <div className="space-y-3">
                      <div className="text-xs font-semibold text-gray-600 mb-2">
                        Or Select Custom Date Range
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Start Date
                          </label>
                          <input
                            type="date"
                            className="w-full border border-gray-300 px-2 py-1 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            value={customStartDate}
                            onChange={(e) => setCustomStartDate(e.target.value)}
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            End Date
                          </label>
                          <input
                            type="date"
                            className="w-full border border-gray-300 px-2 py-1 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            value={customEndDate}
                            onChange={(e) => setCustomEndDate(e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 justify-end">
                        <button
                          onClick={() => {
                            setCustomStartDate("");
                            setCustomEndDate("");
                            if (!dateFilter) {
                              setIsDateDropdownOpen(false);
                            }
                          }}
                          className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-100"
                        >
                          Clear
                        </button>
                        <button
                          onClick={handleCustomDateApply}
                          className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                          disabled={!customStartDate || !customEndDate}
                        >
                          Apply
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>



        {/* KPI Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
          <KPICard
            title="Profiles Submitted"
            currentValue={dashboardData.profilesSubmitted}
            targetRange={`${currentTargets?.targets.profilesSubmitted.min}-${currentTargets?.targets.profilesSubmitted.max}`}
            color="text-blue-500"
            icon={Users}
          />
          <KPICard
            title="Interviews Scheduled"
            currentValue={dashboardData.interviewsScheduled}
            targetRange={`${currentTargets?.targets.interviewsScheduled.min}-${currentTargets?.targets.interviewsScheduled.max}`}
            color="text-green-500"
            icon={Calendar}
          />
          <KPICard
            title="Offers"
            currentValue={dashboardData.offers}
            targetRange={`${currentTargets?.targets.offers.min}-${currentTargets?.targets.offers.max}`}
            color="text-purple-500"
            icon={CheckCircle}
          />
          <KPICard
            title="Joiners"
            currentValue={dashboardData.joiners}
            targetRange={`${currentTargets?.targets.joiners.min}-${currentTargets?.targets.joiners.max}`}
            color="text-red-500"
            icon={UserCheck}
          />
          <KPICard
            title="Requirements Assigned"
            currentValue={dashboardData.requirementsAssigned}
            color="text-green-600"
            icon={Target}
          />
        </div>


        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Candidate Pipeline - Takes 2 columns */}
          <div className="lg:col-span-2 ">
            <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200 min-h-[310px]">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Candidate Pipeline
                </h2>
                <button
                  onClick={handleViewAllPipeline}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                >
                  View All
                  <ArrowRight className="w-4 h-4 ml-1" />
                </button>
              </div>

              <div className="flex space-x-4 overflow-x-auto pb-1">
                <PipelineStage
                  title="Sourced"
                  candidates={pipelineData.sourced.candidates}
                  count={pipelineData.sourced.count}
                  color="bg-blue-500"
                  hasMore={pipelineData.sourced.hasMore}
                />
                <PipelineStage
                  title="Screened"
                  candidates={pipelineData.screened.candidates}
                  count={pipelineData.screened.count}
                  color="bg-yellow-500"
                  hasMore={pipelineData.screened.hasMore}
                />
                <PipelineStage
                  title="Submitted"
                  candidates={pipelineData.submitted.candidates}
                  count={pipelineData.submitted.count}
                  color="bg-purple-500"
                  hasMore={pipelineData.submitted.hasMore}
                />
                <PipelineStage
                  title="Interview"
                  candidates={pipelineData.interview.candidates}
                  count={pipelineData.interview.count}
                  color="bg-indigo-500"
                  hasMore={pipelineData.interview.hasMore}
                />
                <PipelineStage
                  title="Offer"
                  candidates={pipelineData.offer.candidates}
                  count={pipelineData.offer.count}
                  color="bg-orange-500"
                  hasMore={pipelineData.offer.hasMore}
                />
                <PipelineStage
                  title="Joined"
                  candidates={pipelineData.joined.candidates}
                  count={pipelineData.joined.count}
                  color="bg-green-500"
                  hasMore={pipelineData.joined.hasMore}
                />
              </div>
            </div>
          </div>

          {/* To-Do & Reminders - Takes 1 column */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                To-Do & Reminders
              </h2>
              <div className="space-y-6">
                {todoItems.map((item, index) => (
                  <TodoItem
                    key={index}
                    text={item.text}
                    icon={item.icon}
                    iconColor={item.color}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 mt-5">
          {/* One-Time Hiring Incentive */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg shadow-md p-6 border border-green-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-green-800">One-Time Hiring Incentive</h3>
                <p className="text-sm text-green-600 mt-1">Contract & Project-based Placements</p>
              </div>
              <div className="p-3 rounded-full bg-green-500">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border border-green-100">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Incentive Amount</span>
                  <span className="text-2xl font-bold text-green-600">₹15,000</span>
                </div>
                {/* <p className="text-xs text-gray-500 mt-1">Per successful placement</p> */}
              </div>
            </div>
          </div>

          {/* Full-Time Hiring Incentive */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg shadow-md p-6 border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-blue-800">Full-Time Hiring Incentive</h3>
                <p className="text-sm text-blue-600 mt-1">Permanent & Long-term Placements</p>
              </div>
              <div className="p-3 rounded-full bg-blue-500">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border border-blue-100">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Incentive Amount</span>
                  <span className="text-2xl font-bold text-blue-600">₹25,000</span>
                </div>
                {/* <p className="text-xs text-gray-500 mt-1">Per successful placement</p> */}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-bold">!</span>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-red-800 mb-2">Important Notes</h4>
              <div className="space-y-2 text-sm text-red-700">
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span><strong>Incentive is NOT applicable</strong> for hiring Freshers / Trainees</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span><strong>Incentive is NOT applicable</strong> for non-billable hiring</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span>All placements must be verified and approved by management</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span>Incentive is paid after successful completion of 3 Month period</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* View All Pipeline Candidates Modal */}
      <ViewAllPipelineCandidatesModal
        isOpen={isViewAllModalOpen}
        onClose={() => setIsViewAllModalOpen(false)}
        candidates={selectedPipelineCandidates}
        pipelineStage={selectedPipelineStage}
      />
    </div>
  );
}
