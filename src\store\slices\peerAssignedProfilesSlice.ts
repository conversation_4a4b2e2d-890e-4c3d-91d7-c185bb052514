import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ApiService, type PeerAssignedCandidate, type PeerApproveCandidateRequest } from '@/services/api';


// Peer Assigned Profiles state interface
export interface PeerAssignedProfilesState {
  profiles: PeerAssignedCandidate[];
  loading: boolean;
  error: string | null;
  lastFetched: string | null;
}

// Initial state
const initialState: PeerAssignedProfilesState = {
  profiles: [],
  loading: false,
  error: null,
  lastFetched: null,
};

// Async thunk for fetching peer assigned profiles
export const fetchPeerAssignedProfiles = createAsyncThunk(
  'peerAssignedProfiles/fetchPeerAssignedProfiles',
  async (recruiterName: string, { rejectWithValue }) => {
    try {
      const response = await ApiService.fetchPeerAssignedProfiles(recruiterName);
      return response.candidates;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "An unknown error occurred");
    }
  }
);

// Async thunk for updating peer review status
export const updatePeerReviewStatus = createAsyncThunk(
  'peerAssignedProfiles/updatePeerReviewStatus',
  async (requestData: PeerApproveCandidateRequest, { rejectWithValue }) => {
    try {
      const response = await ApiService.peerApproveCandidate(requestData);
      return {
        candidateId: requestData.candidate_id,
        status: requestData.status,
        comments: requestData.comments,
        response
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "Failed to update status");
    }
  }
);

// Create the slice
const peerAssignedProfilesSlice = createSlice({
  name: 'peerAssignedProfiles',
  initialState,
  reducers: {
    // Clear profiles cache
    clearProfilesCache: (state) => {
      state.profiles = [];
      state.lastFetched = null;
    },
    // Update profile status locally (optimistic update)
    updateProfileStatus: (state, action: PayloadAction<{ candidateId: number; status: string }>) => {
      const { candidateId, status } = action.payload;
      const profileIndex = state.profiles.findIndex(profile => profile.id === candidateId);
      if (profileIndex !== -1) {
        state.profiles[profileIndex].status = status;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch peer assigned profiles
      .addCase(fetchPeerAssignedProfiles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPeerAssignedProfiles.fulfilled, (state, action) => {
        state.loading = false;
        state.profiles = action.payload;
        state.lastFetched = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchPeerAssignedProfiles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update peer review status
      .addCase(updatePeerReviewStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePeerReviewStatus.fulfilled, (state, action) => {
        state.loading = false;
        const { candidateId, status } = action.payload;
        const profileIndex = state.profiles.findIndex(profile => profile.id === candidateId);
        if (profileIndex !== -1) {
          state.profiles[profileIndex].status = status || 'PR-Pending';
        }
        state.error = null;
      })
      .addCase(updatePeerReviewStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearProfilesCache,
  updateProfileStatus,
} = peerAssignedProfilesSlice.actions;

export default peerAssignedProfilesSlice.reducer;
