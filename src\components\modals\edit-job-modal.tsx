import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { X, Trash2 } from "lucide-react";
import { type Job } from "@/store/slices/jobsSlice";
import { ApiService, type EditJobRequest } from "@/services/api";
import { useUser } from "@/contexts/user-context";

interface EditJobModalProps {
    job: Job | null;
    isOpen: boolean;
    onClose: () => void;
    onSave: (updatedJob: Job) => void;
}

interface FileUpload {
    id?: number;
    file?: File;
    filename: string;
    file_data?: string;
    extension: string;
    isExisting?: boolean;
}

// Job types and options
const jobTypes = [
    "Permanent with Client",
    "Permanent with Makonis",
    "Contract",
    "Custom",
];

const modesOfWork = ["Hybrid", "WFO", "WFH"];
const shiftTimings = ["General", "Rotational"];
const jobStatuses = ["Active", "Closed", "On Hold", "Hold"];

const currencies = [
    { code: "INR", label: "INR (LPA)", symbol: "₹" },
    { code: "USD", label: "USD", symbol: "$" },
    { code: "EUR", label: "EUR", symbol: "€" },
    { code: "CAD", label: "CAD", symbol: "C$" },
];

export function EditJobModal({
    job,
    isOpen,
    onClose,
    onSave,
}: EditJobModalProps) {
    const { userId } = useUser();
    const [loading, setLoading] = useState(false);
    const [files, setFiles] = useState<FileUpload[]>([]);
    const [existingFileIds, setExistingFileIds] = useState<number[]>([]);

    // Form state
    const [formData, setFormData] = useState({
        client: "",
        role: "",
        experience_min: "",
        experience_max: "",
        budget_min: "",
        budget_max: "",
        currency_type_min: "INR",
        currency_type_max: "INR",
        location: "",
        shift_timings: "General",
        notice_period: "",
        detailed_jd: "",
        mode: "Hybrid",
        job_status: "Active",
        skills: "",
        Job_Type: "Permanent with Client",
        no_of_positions: "",
        Job_Type_details: "",
        Custom_Job_Type: "",
        country: "",
        recruiter: "",
    });

    // Initialize form data when job changes

    useEffect(() => {
        if (job && isOpen) {
const [budget_min_type, budget_min] = job.budget_min.split(" ");
  const [budget_max_type, budget_max] = job.budget_max.split(" ");
            setFormData({
                client: job.client || "",
                role: job.role || "",
                experience_min: job.experience_min || "",
                experience_max: job.experience_max || "",
                budget_min: budget_min || "",
                budget_max: budget_max || "",
                currency_type_min: budget_min_type, // Default, as this info might not be in Job type
                currency_type_max:budget_max_type, // Default
                location: job.location || "",
                shift_timings: job.shift_timings || "General",
                notice_period: job.notice_period || "",
                detailed_jd: job.detailed_jd || "",
                mode: job.mode || "Hybrid",
                job_status: job.job_status || "Active",
                skills: job.skills || "",
                Job_Type: job.job_type || "Permanent with Client",
                no_of_positions: job.no_of_positions || "",
                Job_Type_details: job.custom_job_type || "",
                Custom_Job_Type: job.custom_job_type || "",
                country: job.country || "",
                recruiter: job.recruiter || "",
            });
            setFiles([]);
            setExistingFileIds([]);
        }
    }, [job, isOpen]);

    const handleInputChange = (field: string, value: string | number) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleFileUpload = async (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const selectedFiles = Array.from(event.target.files || []);

        for (const file of selectedFiles) {
            const base64Data = await convertFileToBase64(file);
            const extension = file.name.split(".").pop() || "";

            const newFile: FileUpload = {
                file,
                filename: file.name,
                file_data: base64Data,
                extension,
                isExisting: false,
            };

            setFiles((prev) => [...prev, newFile]);
        }
    };

    const convertFileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const result = reader.result as string;
                // Remove the data URL prefix to get just the base64 data
                const base64Data = result.split(",")[1];
                resolve(base64Data);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    };

    const handleRemoveFile = (index: number) => {
        const file = files[index];
        if (file.isExisting && file.id) {
            // If it's an existing file, remove it from existing file IDs
            setExistingFileIds((prev) => prev.filter((id) => id !== file.id));
        }
        setFiles((prev) => prev.filter((_, i) => i !== index));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!job || !userId) {
            toast.error("Missing job or user ID");
            return;
        }

        setLoading(true);

        try {
            const requestData: EditJobRequest = {
                user_id: userId,
                client: formData.client,
                experience_min: Number(formData.experience_min) || 0,
                experience_max: Number(formData.experience_max) || 0,
                budget_min: Number(formData.budget_min) || 0,
                budget_max: Number(formData.budget_max) || 0,
                currency_type_min: formData.currency_type_min,
                currency_type_max: formData.currency_type_max,
                location: formData.location,
                shift_timings: formData.shift_timings,
                notice_period: formData.notice_period,
                role: formData.role,
                detailed_jd: formData.detailed_jd,
                mode: formData.mode,
                job_status: formData.job_status,
                skills: formData.skills,
                Job_Type: formData.Job_Type,
                no_of_positions: Number(formData.no_of_positions) || 1,
                Job_Type_details: formData.Job_Type_details,
                Custom_Job_Type: formData.Custom_Job_Type,
                country: formData.country,
                recruiter: [formData.recruiter],
                existing_file_ids: existingFileIds.length > 0 ? existingFileIds : undefined,
                pdfs: files
                    .filter((file) => !file.isExisting && file.file_data)
                    .map((file) => ({
                        filename: file.filename,
                        file_data: file.file_data!,
                        extension: file.extension,
                    })),
            };

            const response = await ApiService.editJob(job.id, requestData);

            if (response.status === "success") {
                // Create updated job object
                const updatedJob: Job = {
                    ...job,
                    client: formData.client,
                    role: formData.role,
                    experience_min: formData.experience_min,
                    experience_max: formData.experience_max,
                    budget_min: formData.budget_min,
                    budget_max: formData.budget_max,
                    location: formData.location,
                    shift_timings: formData.shift_timings,
                    notice_period: formData.notice_period,
                    detailed_jd: formData.detailed_jd,
                    mode: formData.mode,
                    job_status: formData.job_status as "Active" | "Closed" | "On Hold" | "Hold",
                    skills: formData.skills,
                    job_type: formData.Job_Type,
                    no_of_positions: formData.no_of_positions,
                    custom_job_type: formData.Custom_Job_Type,
                    data_updated_date: new Date().toISOString().split("T")[0],
                };

                onSave(updatedJob);
                onClose();
                toast.success("Job updated successfully!");
            } else {
                throw new Error(response.message || "Failed to update job");
            }
        } catch (error) {
            console.error("Error updating job:", error);
            toast.error(
                `Failed to update job: ${error instanceof Error ? error.message : "Unknown error"}`
            );
        } finally {
            setLoading(false);
        }
    };

    if (!job) return null;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center justify-between">
                        Edit Job - {job.role} at {job.client}
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClose}
                            className="text-gray-500 hover:text-gray-700"
                        >
                            {/* <X className="h-4 w-4" /> */}
                        </Button>
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="client">Client *</Label>
                            <Input
                                id="client"
                                value={formData.client}
                                onChange={(e) => handleInputChange("client", e.target.value)}
                                required
                            />
                        </div>

                        <div>
                            <Label htmlFor="role">Role *</Label>
                            <Input
                                id="role"
                                value={formData.role}
                                onChange={(e) => handleInputChange("role", e.target.value)}
                                required
                            />
                        </div>

                        <div>
                            <Label htmlFor="location">Location *</Label>
                            <Input
                                id="location"
                                value={formData.location}
                                onChange={(e) => handleInputChange("location", e.target.value)}
                                required
                            />
                        </div>

                        <div>
                            <Label htmlFor="no_of_positions">Number of Positions</Label>
                            <Input
                                id="no_of_positions"
                                type="number"
                                min="1"
                                value={formData.no_of_positions}
                                onChange={(e) => handleInputChange("no_of_positions", e.target.value)}
                            />
                        </div>
                    </div>

                    {/* Experience Range */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Experience Requirements</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="experience_min">Minimum Experience (Years) *</Label>
                                <Input
                                    id="experience_min"
                                    type="number"
                                    min="0"
                                    step="0.5"
                                    value={formData.experience_min}
                                    onChange={(e) => handleInputChange("experience_min", e.target.value)}
                                    required
                                />
                            </div>

                            <div>
                                <Label htmlFor="experience_max">Maximum Experience (Years) *</Label>
                                <Input
                                    id="experience_max"
                                    type="number"
                                    min="0"
                                    step="0.5"
                                    value={formData.experience_max}
                                    onChange={(e) => handleInputChange("experience_max", e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                    </div>

                    {/* Budget Range */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Budget Range</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="budget_min">Minimum Budget *</Label>
                                <div className="flex gap-2">
                                    <Input
                                        id="budget_min"
                                        type="number"
                                        min="0"
                                        step="0.1"
                                        value={formData.budget_min}
                                        onChange={(e) => handleInputChange("budget_min", e.target.value)}
                                        required
                                        className="flex-1"
                                    />
                                    <Select
                                        value={formData.currency_type_min}
                                        onValueChange={(value) => handleInputChange("currency_type_min", value)}
                                    >
                                        <SelectTrigger className="w-24">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {currencies.map((currency) => (
                                                <SelectItem key={currency.code} value={currency.code}>
                                                    {currency.code}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="budget_max">Maximum Budget *</Label>
                                <div className="flex gap-2">
                                    <Input
                                        id="budget_max"
                                        type="number"
                                        min="0"
                                        step="0.1"
                                        value={formData.budget_max}
                                        onChange={(e) => handleInputChange("budget_max", e.target.value)}
                                        required
                                        className="flex-1"
                                    />
                                    <Select
                                        value={formData.currency_type_max}
                                        onValueChange={(value) => handleInputChange("currency_type_max", value)}
                                    >
                                        <SelectTrigger className="w-24">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {currencies.map((currency) => (
                                                <SelectItem key={currency.code} value={currency.code}>
                                                    {currency.code}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Job Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="Job_Type">Job Type *</Label>
                            <Select
                                value={formData.Job_Type}
                                onValueChange={(value) => handleInputChange("Job_Type", value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {jobTypes.map((type) => (
                                        <SelectItem key={type} value={type}>
                                            {type}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="mode">Mode of Work *</Label>
                            <Select
                                value={formData.mode}
                                onValueChange={(value) => handleInputChange("mode", value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {modesOfWork.map((mode) => (
                                        <SelectItem key={mode} value={mode}>
                                            {mode}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="shift_timings">Shift Timings *</Label>
                            <Select
                                value={formData.shift_timings}
                                onValueChange={(value) => handleInputChange("shift_timings", value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {shiftTimings.map((timing) => (
                                        <SelectItem key={timing} value={timing}>
                                            {timing}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="job_status">Job Status *</Label>
                            <Select
                                value={formData.job_status}
                                onValueChange={(value) => handleInputChange("job_status", value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {jobStatuses.map((status) => (
                                        <SelectItem key={status} value={status}>
                                            {status}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="notice_period">Notice Period</Label>
                            <Input
                                id="notice_period"
                                value={formData.notice_period}
                                onChange={(e) => handleInputChange("notice_period", e.target.value)}
                                placeholder="e.g., Immediate, 15 days, 1 month"
                            />
                        </div>

                        {formData.Job_Type === "Custom" && (
                            <div>
                                <Label htmlFor="Custom_Job_Type">Custom Job Type Details</Label>
                                <Input
                                    id="Custom_Job_Type"
                                    value={formData.Custom_Job_Type}
                                    onChange={(e) => handleInputChange("Custom_Job_Type", e.target.value)}
                                />
                            </div>
                        )}
                    </div>

                    {/* Text Areas */}
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="skills">Required Skills *</Label>
                            <Textarea
                                id="skills"
                                value={formData.skills}
                                onChange={(e) => handleInputChange("skills", e.target.value)}
                                rows={3}
                                placeholder="List the key skills required for this position"
                                required
                            />
                        </div>

                        <div>
                            <Label htmlFor="detailed_jd">Detailed Job Description *</Label>
                            <Textarea
                                id="detailed_jd"
                                value={formData.detailed_jd}
                                onChange={(e) => handleInputChange("detailed_jd", e.target.value)}
                                rows={6}
                                placeholder="Provide a detailed job description including responsibilities, requirements, and benefits"
                                required
                            />
                        </div>

                        {formData.Job_Type !== "Custom" && (
                            <div>
                                <Label htmlFor="Job_Type_details">Additional Job Type Details</Label>
                                <Textarea
                                    id="Job_Type_details"
                                    value={formData.Job_Type_details}
                                    onChange={(e) => handleInputChange("Job_Type_details", e.target.value)}
                                    rows={3}
                                    placeholder="Any additional details specific to the job type"
                                />
                            </div>
                        )}
                    </div>

                    {/* File Upload Section */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Job Description Files</h3>
                        <div>
                            <Label htmlFor="file-upload">Upload Job Description (PDF)</Label>
                            <Input
                                id="file-upload"
                                type="file"
                                multiple
                                accept=".pdf"
                                onChange={handleFileUpload}
                                className="mt-1"
                            />
                        </div>

                        {files.length > 0 && (
                            <div className="space-y-2">
                                <h4 className="font-medium">Uploaded Files:</h4>
                                {files.map((file, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center justify-between p-2 bg-gray-50 rounded"
                                    >
                                        <span className="text-sm">{file.filename}</span>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleRemoveFile(index)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-4 pt-6">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={loading}
                            className="bg-blue-600 hover:bg-blue-700"
                        >
                            {loading ? "Updating..." : "Update Job"}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
}
