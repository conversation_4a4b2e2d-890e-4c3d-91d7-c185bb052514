import React, { useEffect } from "react";
import { X, FileText } from "lucide-react";
import { sopData, getColorClasses } from "@/data/sop-data";

interface SOPModalProps {
    isOpen: boolean;
    onClose: () => void;
}

export function SOPModal({ isOpen, onClose }: SOPModalProps) {
    // Handle escape key press
    useEffect(() => {
        const handleEscapeKey = (event: KeyboardEvent) => {
            if (event.key === "Escape") {
                onClose();
            }
        };

        let previousOverflow = "";
        if (isOpen) {
            document.addEventListener("keydown", handleEscapeKey);
            // Prevent body scroll when modal is open
            previousOverflow = document.body.style.overflow;
            document.body.style.overflow = "hidden";
        }

        return () => {
            document.removeEventListener("keydown", handleEscapeKey);
            // Restore to previous inline style (empty string falls back to CSS rules)
            document.body.style.overflow = previousOverflow;
        };
    }, [isOpen, onClose]);

    // Handle backdrop click
    const handleBackdropClick = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 backdrop-blur-md bg-white/10 backdrop-saturate-150 flex items-center justify-center z-50 p-4 transition-all">
            <div className="pointer-events-auto" onClick={handleBackdropClick}>
                <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                    {/* Header */}
                    <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 relative">
                        <button
                            onClick={onClose}
                            className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors"
                        >
                            <X className="h-6 w-6" />
                        </button>
                        <div className="flex items-center">
                            <FileText className="h-8 w-8 mr-3" />
                            <div>
                                <h2 className="text-xl font-bold text-white">
                                    Standard Operating Procedures (SOP)
                                </h2>
                                <p className="text-sm text-blue-100">
                                    Access all company procedures and guidelines
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Content */}
                    <div className="p-6 overflow-y-auto max-h-[calc(90vh-110px)]">
                        <div className="mb-6">
                            <p className="text-gray-600 text-sm">
                                Complete guide to our recruitment and selection processes.
                                Follow these Standard Operating Procedures for consistent and effective recruitment.
                            </p>
                        </div>

                        {/* SOP Content - Scrollable */}
                        <div className="space-y-6">
                            {/* Dynamic SOP Sections */}
                            {sopData.map((section) => {
                                const colors = getColorClasses(section.color);
                                return (
                                    <div key={section.id} id={section.id} className="group">
                                        <div className="flex items-center gap-4 mb-4">
                                            <div className={`flex-shrink-0 w-10 h-10 ${colors.bg} rounded-full flex items-center justify-center text-white font-bold text-base`}>
                                                {section.number}
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-lg font-bold text-gray-900 mb-2">
                                                    {section.title}
                                                </h3>
                                                <div className={`w-16 h-1 ${colors.bg} rounded-full`}></div>
                                            </div>
                                        </div>

                                        <div className="ml-14 space-y-3">
                                            {section.subsections.map((subsection) => (
                                                <div key={subsection.id} className={`bg-gradient-to-r ${colors.bgLight} border ${colors.border} rounded-lg p-4 hover:shadow-md transition-all duration-300`}>
                                                    <div className="flex items-start justify-between mb-3">
                                                        <div>
                                                            <h4 className={`text-base font-semibold ${colors.text} mb-2`}>
                                                                {subsection.id}. {subsection.title}
                                                            </h4>
                                                            <p className={`${colors.textLight} mb-2`}>{subsection.description}</p>
                                                        </div>
                                                    </div>

                                                    {/* Roles */}
                                                    {subsection.roles && subsection.roles.length > 0 && (
                                                        <div className="mb-3">
                                                            <p className={`text-xs font-medium ${colors.text} mb-1`}>Responsible:</p>
                                                            <div className="flex flex-wrap gap-1">
                                                                {subsection.roles.map((role, index) => (
                                                                    <span key={index} className={`text-xs px-2 py-1 ${colors.bg} text-white rounded-full`}>
                                                                        {role}
                                                                    </span>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    )}

                                                    {/* Points */}
                                                    <div className="space-y-1.5">
                                                        {subsection.points.map((point, index) => (
                                                            <div key={index} className={`flex items-center gap-2.5 ${colors.textDark}`}>
                                                                <div className={`w-1.5 h-1.5 ${colors.dot} rounded-full`}></div>
                                                                <span className="text-sm">{point}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        {/* Additional Information */}
                        {/* <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 className="font-semibold text-blue-900 mb-2">
                                Need Help?
                            </h3>
                            <p className="text-blue-800 text-sm mb-3">
                                If you have questions about any SOP or need clarification on procedures,
                                please contact your manager or the HR team.
                            </p>
                            <div className="flex gap-2">
                                <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm">
                                    Contact HR
                                </button>
                                <button className="bg-white text-blue-600 border border-blue-600 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors text-sm">
                                    Request Training
                                </button>
                            </div>
                        </div> */}
                    </div>
                </div>
            </div>
        </div>
    );
}
