import { store } from '@/store';
import { updateCandidateStatus, updateCandidateData } from '@/store/slices/candidatesSlice';
import { updateJobStatus, updateJobData } from '@/store/slices/jobsSlice';
import { updateProfileStatus, updateProfileData } from '@/store/slices/peerAssignedProfilesSlice';

export interface SSEEvent {
    type: 'candidate_update' | 'candidate_data_update' | 'job_update' | 'job_data_update' | 'profile_update' | 'profile_data_update' | 'system_message';
    data: any;
    timestamp: string;
}

// Legacy event types from your existing implementation
export interface LegacySSEEvent {
    event_type: 'candidate_added' | 'candidate_updated' | 'candidate_editing' |
    'peer_review_assigned' | 'peer_review_status_updated' |
    'job_created' | 'job_updated' | 'job_editing';
    id: number;
    name?: string;
    role?: string;
    recruiter?: string;
    [key: string]: any;
}

export interface CandidateUpdateEvent {
    candidateId: number;
    status: string;
    comment?: string;
    updatedBy: string;
    updatedAt: string;
}

// Enhanced interface for complete candidate data updates
export interface CandidateDataUpdateEvent {
    candidateId: number;
    candidateData: any; // Complete candidate object
    isNew: boolean; // Flag to indicate if this is a new candidate
    updatedBy: string;
    updatedAt: string;
}

export interface JobUpdateEvent {
    jobId: number;
    status: string;
    updatedBy: string;
    updatedAt: string;
}

// Enhanced interface for complete job data updates
export interface JobDataUpdateEvent {
    jobId: number;
    jobData: any; // Complete job object
    isNew: boolean; // Flag to indicate if this is a new job
    updatedBy: string;
    updatedAt: string;
}

export interface ProfileUpdateEvent {
    profileId: number;
    status: string;
    updatedBy: string;
    updatedAt: string;
}

// Enhanced interface for complete profile data updates
export interface ProfileDataUpdateEvent {
    profileId: number;
    profileData: any; // Complete profile object
    isNew: boolean; // Flag to indicate if this is a new profile
    updatedBy: string;
    updatedAt: string;
}

class SSEService {
    private eventSource: EventSource | null = null;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000; // Start with 1 second
    private isConnected = false;
    private userId: string | null = null;
    private userRole: string | null = null;
    private listeners: Set<(event: SSEEvent) => void> = new Set();

    constructor() {
        this.handleMessage = this.handleMessage.bind(this);
        this.handleError = this.handleError.bind(this);
        this.handleOpen = this.handleOpen.bind(this);
    }

    /**
     * Connect to SSE endpoint
     */
    connect(userId: string, userRole: string): void {
        if (this.eventSource && this.eventSource.readyState === EventSource.OPEN) {
            return; // Already connected
        }

        this.userId = userId;
        this.userRole = userRole;

        try {
            // Use your existing SSE endpoint
            const userName = localStorage.getItem("userName");
            const sseUrl = `http://142.93.222.128:8081/events?userid=${encodeURIComponent(userId)}&username=${encodeURIComponent(userName || '')}`;

            this.eventSource = new EventSource(sseUrl);

            this.eventSource.onopen = this.handleOpen;
            this.eventSource.onmessage = this.handleMessage;
            this.eventSource.onerror = this.handleError;

        } catch (error) {
            console.error('Failed to connect to SSE:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * Disconnect from SSE
     */
    disconnect(): void {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
        this.reconnectAttempts = 0;
    }

    /**
     * Add event listener
     */
    addEventListener(listener: (event: SSEEvent) => void): void {
        this.listeners.add(listener);
    }

    /**
     * Remove event listener
     */
    removeEventListener(listener: (event: SSEEvent) => void): void {
        this.listeners.delete(listener);
    }

    /**
     * Check if connected
     */
    getConnectionStatus(): boolean {
        return this.isConnected && this.eventSource?.readyState === EventSource.OPEN;
    }

    private handleOpen(): void {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
    }

    private handleMessage(event: MessageEvent): void {
        try {
            const data = JSON.parse(event.data);

            // Handle your existing legacy event format
            if (data.event_type) {
                this.handleLegacyEvent(data);
            } else {
                // Handle new format if needed
                const sseEvent: SSEEvent = data;
                this.updateReduxStore(sseEvent);
                this.listeners.forEach(listener => listener(sseEvent));
            }

        } catch (error) {
            console.error('Failed to parse SSE message:', error);
        }
    }

    private handleError(): void {
        this.isConnected = false;

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        this.scheduleReconnect();
    }

    private scheduleReconnect(): void {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

        setTimeout(() => {
            if (this.userId && this.userRole) {
                this.connect(this.userId, this.userRole);
            }
        }, delay);
    }

    private handleLegacyEvent(data: LegacySSEEvent): void {
        const dispatch = store.dispatch;

        // Handle candidate events
        if (
            data.event_type === "candidate_added" ||
            data.event_type === "candidate_updated" ||
            data.event_type === "candidate_editing" ||
            data.event_type === "peer_review_status_updated"
        ) {
            // Check if we have complete candidate data or just status update
            const hasCompleteData = this.hasCompleteCandidateData(data);

            if (hasCompleteData) {
                // Handle complete candidate data update
                const candidateData = this.extractCandidateData(data);
                const isNew = data.event_type === "candidate_added";

                dispatch(updateCandidateData({
                    candidateData,
                    isNew
                }));

                // Create SSE event for listeners
                const sseEvent: SSEEvent = {
                    type: 'candidate_data_update',
                    data: {
                        candidateId: data.id,
                        candidateData,
                        isNew,
                        updatedBy: data.recruiter || 'Unknown',
                        updatedAt: new Date().toISOString()
                    },
                    timestamp: new Date().toISOString()
                };

                this.listeners.forEach(listener => listener(sseEvent));
            } else {
                // Handle status-only update
                dispatch(updateCandidateStatus({
                    id: data.id,
                    status: data.status || 'Updated',
                    comment: data.comment,
                }));

                // Create SSE event for listeners
                const sseEvent: SSEEvent = {
                    type: 'candidate_update',
                    data: {
                        candidateId: data.id,
                        name: data.name,
                        status: data.status || 'Updated',
                        comment: data.comment,
                        updatedBy: data.recruiter || 'Unknown',
                        updatedAt: new Date().toISOString()
                    },
                    timestamp: new Date().toISOString()
                };

                this.listeners.forEach(listener => listener(sseEvent));
            }

        } else if (data.event_type === "peer_review_assigned") {
            // Handle peer review assignment
            dispatch(updateProfileStatus({
                candidateId: data.id,
                status: 'PR-Pending'
            }));

            // Create SSE event for listeners
            const sseEvent: SSEEvent = {
                type: 'profile_update',
                data: {
                    profileId: data.id,
                    status: 'PR-Pending',
                    updatedBy: data.recruiter || 'Unknown',
                    updatedAt: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            };

            this.listeners.forEach(listener => listener(sseEvent));

        } else if (
            data.event_type === "job_created" ||
            data.event_type === "job_updated" ||
            data.event_type === "job_editing"
        ) {
            // Check if we have complete job data or just status update
            const hasCompleteJobData = this.hasCompleteJobData(data);

            if (hasCompleteJobData) {
                // Handle complete job data update
                const jobData = this.extractJobData(data);
                const isNew = data.event_type === "job_created";

                dispatch(updateJobData({
                    jobData,
                    isNew
                }));

                // Create SSE event for listeners
                const sseEvent: SSEEvent = {
                    type: 'job_data_update',
                    data: {
                        jobId: data.id,
                        jobData,
                        isNew,
                        updatedBy: data.recruiter || 'Unknown',
                        updatedAt: new Date().toISOString()
                    },
                    timestamp: new Date().toISOString()
                };

                this.listeners.forEach(listener => listener(sseEvent));
            } else {
                // Handle status-only update
                dispatch(updateJobStatus({
                    id: data.id,
                    status: data.status || 'Updated'
                }));

                // Create SSE event for listeners
                const sseEvent: SSEEvent = {
                    type: 'job_update',
                    data: {
                        jobId: data.id,
                        status: data.status || 'Updated',
                        updatedBy: data.recruiter || 'Unknown',
                        updatedAt: new Date().toISOString()
                    },
                    timestamp: new Date().toISOString()
                };

                this.listeners.forEach(listener => listener(sseEvent));
            }
        }
    }

    private updateReduxStore(event: SSEEvent): void {
        const dispatch = store.dispatch;

        switch (event.type) {
            case 'candidate_update':
                const candidateUpdate = event.data as CandidateUpdateEvent;
                dispatch(updateCandidateStatus({
                    id: candidateUpdate.candidateId,
                    status: candidateUpdate.status,
                    comment: candidateUpdate.comment
                }));
                break;

            case 'candidate_data_update':
                const candidateDataUpdate = event.data as CandidateDataUpdateEvent;
                dispatch(updateCandidateData({
                    candidateData: candidateDataUpdate.candidateData,
                    isNew: candidateDataUpdate.isNew
                }));
                break;

            case 'job_update':
                const jobUpdate = event.data as JobUpdateEvent;
                dispatch(updateJobStatus({
                    id: jobUpdate.jobId,
                    status: jobUpdate.status
                }));
                break;

            case 'job_data_update':
                const jobDataUpdate = event.data as JobDataUpdateEvent;
                dispatch(updateJobData({
                    jobData: jobDataUpdate.jobData,
                    isNew: jobDataUpdate.isNew
                }));
                break;

            case 'profile_update':
                const profileUpdate = event.data as ProfileUpdateEvent;
                dispatch(updateProfileStatus({
                    candidateId: profileUpdate.profileId,
                    status: profileUpdate.status
                }));
                break;

            case 'profile_data_update':
                const profileDataUpdate = event.data as ProfileDataUpdateEvent;
                dispatch(updateProfileData({
                    profileData: profileDataUpdate.profileData,
                    isNew: profileDataUpdate.isNew
                }));
                break;

            case 'system_message':
                // Handle system messages (notifications, etc.)
                break;

            default:
                console.warn('Unknown SSE event type:', event.type);
        }
    }

    /**
     * Check if the legacy event contains complete candidate data
     */
    private hasCompleteCandidateData(data: LegacySSEEvent): boolean {
        // Check for essential candidate fields beyond just id and status
        const requiredFields = ['name', 'email', 'phone'];
        return requiredFields.some(field => data[field] !== undefined && data[field] !== null);
    }

    /**
     * Extract candidate data from legacy SSE event
     */
    private extractCandidateData(data: LegacySSEEvent): any {
        return {
            id: data.id,
            name: data.name || 'Unknown',
            email: data.email || '',
            phone: data.phone || '',
            client: data.client || 'N/A',
            profile: data.profile || data.role || 'N/A',
            skills: data.skills || '',
            status: data.status || 'Updated',
            recruiter: data.recruiter || '',
            comment: data.comment || '',
            jobId: data.job_id || data.jobId || 'N/A',
            appliedDate: data.applied_date || data.appliedDate || new Date().toISOString(),
            lastUpdated: new Date().toISOString().split('T')[0],
            // Add other fields as available in the SSE data
            experience: data.experience || 0,
            education: data.education || '',
            location: data.location || '',
            salary: data.salary || 'N/A - N/A',
            notes: data.notes || '',
            source: 'SSE',
            peerReviewer: data.peer_reviewer || data.peerReviewer || '',
            management: data.management || null,
            clientAssigned: data.client_assigned || data.clientAssigned || false,
        };
    }

    /**
     * Check if the legacy event contains complete job data
     */
    private hasCompleteJobData(data: LegacySSEEvent): boolean {
        // Check for essential job fields beyond just id and status
        const requiredFields = ['role', 'client', 'skills'];
        return requiredFields.some(field => data[field] !== undefined && data[field] !== null);
    }

    /**
     * Extract job data from legacy SSE event
     */
    private extractJobData(data: LegacySSEEvent): any {
        return {
            id: data.id,
            role: data.role || 'Unknown Role',
            client: data.client || 'Unknown Client',
            skills: data.skills || '',
            recruiter: data.recruiter || '',
            job_status: data.status || 'Active',
            location: data.location || '',
            experience_min: data.experience_min || data.experienceMin || '0',
            experience_max: data.experience_max || data.experienceMax || '10',
            budget_min: data.budget_min || data.budgetMin || '0',
            budget_max: data.budget_max || data.budgetMax || '0',
            no_of_positions: data.no_of_positions || data.positions || '1',
            job_type: data.job_type || data.jobType || 'Full Time',
            mode: data.mode || 'Remote',
            country: data.country || 'India',
            management: data.management || '',
            detailed_jd: data.detailed_jd || data.description || '',
            date_created: data.date_created || new Date().toISOString(),
            time_created: data.time_created || new Date().toLocaleTimeString(),
            data_updated_date: new Date().toISOString().split('T')[0],
            data_updated_time: new Date().toLocaleTimeString(),
            notice_period: data.notice_period || data.noticePeriod || 'Immediate',
            shift_timings: data.shift_timings || data.shiftTimings || '9 AM - 6 PM',
            contract_in_months: data.contract_in_months || data.contractMonths || null,
            custom_job_type: data.custom_job_type || data.customJobType || null,
            jd_pdf_present: data.jd_pdf_present || data.jdPdfPresent || false,
            jd_pdf_extension: data.jd_pdf_extension || data.jdPdfExtension || null,
        };
    }
}

// Export singleton instance
export const sseService = new SSEService();
