import { store } from '@/store';
import { updateCandidateStatus } from '@/store/slices/candidatesSlice';
import { updateJobStatus } from '@/store/slices/jobsSlice';
import { updateProfileStatus } from '@/store/slices/peerAssignedProfilesSlice';

export interface SSEEvent {
    type: 'candidate_update' | 'job_update' | 'profile_update' | 'system_message';
    data: any;
    timestamp: string;
}

// Legacy event types from your existing implementation
export interface LegacySSEEvent {
    event_type: 'candidate_added' | 'candidate_updated' | 'candidate_editing' |
    'peer_review_assigned' | 'peer_review_status_updated' |
    'job_created' | 'job_updated' | 'job_editing';
    id: number;
    name?: string;
    role?: string;
    recruiter?: string;
    [key: string]: any;
}

export interface CandidateUpdateEvent {
    candidateId: number;
    status: string;
    comment?: string;
    updatedBy: string;
    updatedAt: string;
}

export interface JobUpdateEvent {
    jobId: number;
    status: string;
    updatedBy: string;
    updatedAt: string;
}

export interface ProfileUpdateEvent {
    profileId: number;
    status: string;
    updatedBy: string;
    updatedAt: string;
}

class SSEService {
    private eventSource: EventSource | null = null;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000; // Start with 1 second
    private isConnected = false;
    private userId: string | null = null;
    private userRole: string | null = null;
    private listeners: Set<(event: SSEEvent) => void> = new Set();

    constructor() {
        this.handleMessage = this.handleMessage.bind(this);
        this.handleError = this.handleError.bind(this);
        this.handleOpen = this.handleOpen.bind(this);
    }

    /**
     * Connect to SSE endpoint
     */
    connect(userId: string, userRole: string): void {
        if (this.eventSource && this.eventSource.readyState === EventSource.OPEN) {
            return; // Already connected
        }

        this.userId = userId;
        this.userRole = userRole;

        try {
            // Use your existing SSE endpoint
            const userName = localStorage.getItem("userName");
            const sseUrl = `http://142.93.222.128:8081/events?userid=${encodeURIComponent(userId)}&username=${encodeURIComponent(userName || '')}`;

            this.eventSource = new EventSource(sseUrl);

            this.eventSource.onopen = this.handleOpen;
            this.eventSource.onmessage = this.handleMessage;
            this.eventSource.onerror = this.handleError;

        } catch (error) {
            console.error('Failed to connect to SSE:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * Disconnect from SSE
     */
    disconnect(): void {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
        this.reconnectAttempts = 0;
    }

    /**
     * Add event listener
     */
    addEventListener(listener: (event: SSEEvent) => void): void {
        this.listeners.add(listener);
    }

    /**
     * Remove event listener
     */
    removeEventListener(listener: (event: SSEEvent) => void): void {
        this.listeners.delete(listener);
    }

    /**
     * Check if connected
     */
    getConnectionStatus(): boolean {
        return this.isConnected && this.eventSource?.readyState === EventSource.OPEN;
    }

    private handleOpen(): void {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
    }

    private handleMessage(event: MessageEvent): void {
        try {
            const data = JSON.parse(event.data);

            // Handle your existing legacy event format
            if (data.event_type) {
                this.handleLegacyEvent(data);
            } else {
                // Handle new format if needed
                const sseEvent: SSEEvent = data;
                this.updateReduxStore(sseEvent);
                this.listeners.forEach(listener => listener(sseEvent));
            }

        } catch (error) {
            console.error('Failed to parse SSE message:', error);
        }
    }

    private handleError(): void {
        this.isConnected = false;

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        this.scheduleReconnect();
    }

    private scheduleReconnect(): void {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

        setTimeout(() => {
            if (this.userId && this.userRole) {
                this.connect(this.userId, this.userRole);
            }
        }, delay);
    }

    private handleLegacyEvent(data: LegacySSEEvent): void {
        const dispatch = store.dispatch;

        // Handle candidate events
        if (
            data.event_type === "candidate_added" ||
            data.event_type === "candidate_updated" ||
            data.event_type === "candidate_editing" ||
            data.event_type === "peer_review_status_updated"
        ) {
            // Update candidate status in Redux
            dispatch(updateCandidateStatus({
                id: data.id,
                status: data.status || 'Updated',
                comment: data.comment
                
            }));

            // Create SSE event for listeners
            const sseEvent: SSEEvent = {
                type: 'candidate_update',
                data: {
                    candidateId: data.id,
                    name: data.name,
                    status: data.status || 'Updated',
                    comment: data.comment,
                    updatedBy: data.recruiter || 'Unknown',
                    updatedAt: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            };

            this.listeners.forEach(listener => listener(sseEvent));

        } else if (data.event_type === "peer_review_assigned") {
            // Handle peer review assignment
            dispatch(updateProfileStatus({
                candidateId: data.id,
                status: 'PR-Pending'
            }));

            // Create SSE event for listeners
            const sseEvent: SSEEvent = {
                type: 'profile_update',
                data: {
                    profileId: data.id,
                    status: 'PR-Pending',
                    updatedBy: data.recruiter || 'Unknown',
                    updatedAt: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            };

            this.listeners.forEach(listener => listener(sseEvent));

        } else if (
            data.event_type === "job_created" ||
            data.event_type === "job_updated" ||
            data.event_type === "job_editing"
        ) {
            // Handle job updates
            dispatch(updateJobStatus({
                id: data.id,
                status: data.status || 'Updated'
            }));

            // Create SSE event for listeners
            const sseEvent: SSEEvent = {
                type: 'job_update',
                data: {
                    jobId: data.id,
                    status: data.status || 'Updated',
                    updatedBy: data.recruiter || 'Unknown',
                    updatedAt: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            };

            this.listeners.forEach(listener => listener(sseEvent));
        }
    }

    private updateReduxStore(event: SSEEvent): void {
        const dispatch = store.dispatch;

        switch (event.type) {
            case 'candidate_update':
                const candidateUpdate = event.data as CandidateUpdateEvent;
                dispatch(updateCandidateStatus({
                    id: candidateUpdate.candidateId,
                    status: candidateUpdate.status,
                    comment: candidateUpdate.comment
                }));
                break;

            case 'job_update':
                const jobUpdate = event.data as JobUpdateEvent;
                dispatch(updateJobStatus({
                    id: jobUpdate.jobId,
                    status: jobUpdate.status
                }));
                break;

            case 'profile_update':
                const profileUpdate = event.data as ProfileUpdateEvent;
                dispatch(updateProfileStatus({
                    candidateId: profileUpdate.profileId,
                    status: profileUpdate.status
                }));
                break;

            case 'system_message':
                // Handle system messages (notifications, etc.)
                break;

            default:
                console.warn('Unknown SSE event type:', event.type);
        }
    }
}

// Export singleton instance
export const sseService = new SSEService();
