import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Navigate, RouteObject } from "react-router-dom";
import { Suspense, lazy } from "react";
import PeerAssignedProfiles from "./components/peer-assigned-profiles";
import { LoadingSpinner } from "./components/ui/loading-spinner";

import {
  RoleBasedRedirect,
  ProtectedRoute,
  AuthenticatedLayout,
} from "./components/routing/route-components";

// Implement lazy loading for all components
const Dashboard = lazy(() =>
  import("./components/dashboard/dashboard").then((module) => ({
    default: module.Dashboard,
  }))
);
const Login = lazy(() =>
  import("./components/auth/login").then((module) => ({
    default: module.Login,
  }))
);
const Register = lazy(() =>
  import("./components/auth/register").then((module) => ({
    default: module.Register,
  }))
);

const ChangePassword = lazy(() =>
  import("./components/auth/change-password").then((module) => ({
    default: module.ChangePassword,
  }))
);
const AssignedRequirements = lazy(() =>
  import("./components/requirements/assigned-requirements").then((module) => ({
    default: module.AssignedRequirements,
  }))
);
const RegisterCandidate = lazy(() =>
  import("./components/candidates/register-candidate").then((module) => ({
    default: module.RegisterCandidate,
  }))
);
const CalendarView = lazy(() => import("./components/calendar/calendar"));
const JobListing = lazy(() =>
  import("./components/jobs/job-listing").then((module) => ({
    default: module.JobListing,
  }))
);
const JobAssignments = lazy(() =>
  import("./components/jobs/job-assignments").then((module) => ({
    default: module.JobAssignments,
  }))
);
const JobsOverview = lazy(() =>
  import("./components/dashboard/jobs-overview").then((module) => ({
    default: module.JobsOverview,
  }))
);
const JobDetailPage = lazy(() =>
  import("./components/dashboard/job-detail-page").then((module) => ({
    default: module.JobDetailPage,
  }))
);
const UserAccounts = lazy(() =>
  import("./components/users/user-accounts").then((module) => ({
    default: module.UserAccounts,
  }))
);
const ProfileTransfer = lazy(() =>
  import("./components/profile/profile-transfer").then((module) => ({
    default: module.ProfileTransfer,
  }))
);
const ManagerAnalytics = lazy(() =>
  import("./components/analytics/manager-analytics").then((module) => ({
    default: module.ManagerAnalytics,
  }))
);
const RecruiterAnalytics = lazy(() =>
  import("./components/analytics/recruiter-analytics").then((module) => ({
    default: module.RecruiterAnalytics,
  }))
);
const ProfileAnalysis = lazy(() =>
  import("./components/profile-analysis/profile-analysis").then((module) => ({
    default: module.ProfileAnalysis,
  }))
);
const HelpSupport = lazy(() =>
  import("./components/help-support/help-support").then((module) => ({
    default: module.HelpSupport,
  }))
);

const CandidateDashboard = lazy(() =>
  import("./components/dashboard/candidate-dashboard").then((module) => ({
    default: module.CandidateDashboard,
  }))
);


const TalentPoolPage = lazy(() =>
  import("./components/dashboard/talent-pool-page").then((module) => ({
    default: module.TalentPoolPage,
  }))
);

const ClientsPage = lazy(() =>
  import("./components/dashboard/clients-page").then((module) => ({
    default: module.ClientsPage,
  }))
);
const RecruitersPage = lazy(() =>
  import("./components/dashboard/recruiters-page").then((module) => ({
    default: module.RecruitersPage,
  }))
);
const ClientDetailPage = lazy(() =>
  import("./components/dashboard/client-detail-page").then((module) => ({
    default: module.ClientDetailPage,
  }))
);
const RecruiterDetailPage = lazy(() =>
  import("./components/dashboard/recruiter-detail-page").then((module) => ({
    default: module.RecruiterDetailPage,
  }))
);

// Define routes
const routes: RouteObject[] = [
  // Public routes
  {
    path: "/login",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Login />
      </Suspense>
    ),
  },
  {
    path: "/register",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Register />
      </Suspense>
    ),
  },


  // Root redirect based on user role
  {
    path: "/",
    element: (
      <ProtectedRoute>
        <Suspense fallback={<LoadingSpinner />}>
          <RoleBasedRedirect />
        </Suspense>
      </ProtectedRoute>
    ),
  },

  // Common routes - Dashboard redirects to role-specific dashboard
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </ProtectedRoute>
    ),
  },
  // Common routes - Candidate redirects to role-specific candidate
  {
    path: "/candidate",
    element: (
      <ProtectedRoute>
        <Suspense fallback={<LoadingSpinner />}>
          <CandidateDashboard />
        </Suspense>
      </ProtectedRoute>
    ),
  },
  {
    path: "/change-password",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <ChangePassword />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/calendar",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <CalendarView />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/register-candidate",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/help-and-support",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <HelpSupport />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },


  // Manager routes
  {
    path: "/manager/dashboard",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <CandidateDashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },


  {
    path: "/manager/talent-pool",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <TalentPoolPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/job-listing",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobListing />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/jobs-overview",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobsOverview />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/jobs-overview/:jobId",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobDetailPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/job-assignments",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobAssignments />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/register-candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/register-candidate/:jobId",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/profile-transfer",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ProfileTransfer />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/analytics",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ManagerAnalytics />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  // {
  //   path: "/manager/job-candidates/:jobId",
  //   element: (
  //     <AuthenticatedLayout requiredRoles={["manager"]}>
  //       <Suspense fallback={<LoadingSpinner />}>
  //         <JobCandidatesMapping />
  //       </Suspense>
  //     </AuthenticatedLayout>
  //   ),
  // },
  // {
  //   path: "/manager/job-candidates-demo",
  //   element: (
  //     <AuthenticatedLayout requiredRoles={["manager"]}>
  //       <Suspense fallback={<LoadingSpinner />}>
  //         <JobCandidatesDemo />
  //       </Suspense>
  //     </AuthenticatedLayout>
  //   ),
  // },
  {
    path: "/manager/user-accounts",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <UserAccounts />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/peer-assigned-profiles",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <PeerAssignedProfiles />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/clients",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ClientsPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/recruiters",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruitersPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  // Recruiter routes
  {
    path: "/recruiter/dashboard",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <CandidateDashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  {
    path: "/recruiter/requirements",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <AssignedRequirements />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/register-candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/register-candidate/:jobId",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  {
    path: "/recruiter/analytics",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruiterAnalytics />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/profile-analysis",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ProfileAnalysis />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/peer-assigned-profiles",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <PeerAssignedProfiles />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/talent-pool",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <TalentPoolPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/clients",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ClientsPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/recruiters",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruitersPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/clients/:clientId",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ClientDetailPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/recruiters/:recruiterId",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruiterDetailPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/clients/:clientId",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ClientDetailPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/recruiters/:recruiterId",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruiterDetailPage />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  // Catch-all route
  {
    path: "*",
    element: <Navigate to="/dashboard" replace />,
  },
];

// Create router with global error handling
export const router = createBrowserRouter(routes);
