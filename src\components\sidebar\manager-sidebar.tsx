import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useLogoutConfirmation } from "@/hooks/use-logout-confirmation";
import { ProfileAvatar } from "@/components/ui/profile-avatar";
import {
  Calendar,
  Home,
  ClipboardList,
  BarChart2,
  UserCheck,
  Lock,
  LogOut,
  Briefcase,
  UserPlus,
  UserCog,
  Users,
  ArrowRightLeft,
  UserSearch
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface MenuSection {
  title?: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    items: [{ title: "Dashboard", url: "/manager/dashboard", icon: Home }],
  },
  {
    title: "CAND<PERSON>AT<PERSON>",
    items: [
      {
        title: "Candidate Repository",
        url: "/manager/candidate",
        icon: Users,
      },
      {
        title: "Available Candidates",
        url: "/manager/talent-pool",
        icon: UserSearch,
      },
      {
        title: "Register Candidate",
        url: "/manager/register-candidate",
        icon: UserPlus,
      },
      {
        title: "Peer Assigned Profiles",
        url: "/manager/peer-assigned-profiles",
        icon: UserCheck,
      },
      {
        title: "Profile Transfer",
        url: "/manager/profile-transfer",
        icon: ArrowRightLeft,
      },
    ],
  },
  {
    title: "RECRUITMENT",
    items: [
      { title: "Job Listing", url: "/manager/job-listing", icon: Briefcase },
      {
        title: "Job Assignments",
        url: "/manager/job-assignments",
        icon: ClipboardList,
      },
    ],
  },
  {
    title: "GENERAL",
    items: [
      { title: "Analytics", url: "/manager/analytics", icon: BarChart2 },
      { title: "User Accounts", url: "/manager/user-accounts", icon: UserCog },
      { title: "Calendar", url: "/calendar", icon: Calendar },
      { title: "Help and Support", url: "/help-and-support", icon: Users },
      { title: "Change Password", url: "/change-password", icon: Lock },
      { title: "Logout", url: "#", icon: LogOut },
    ],
  },
];

export function ManagerSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;



  const { LogoutDialog, handleLogoutClick } = useLogoutConfirmation();

  const isMenuItemActive = (menuUrl: string) => {
    if (currentPath === menuUrl) return true;

    if (menuUrl === "/manager/dashboard") {
      if (currentPath === "/manager/dashboard") return true;
      if (currentPath.includes("/clients/")) return true;

      const specificRoutes = [
        "/manager/candidate",
        "/manager/analytics",
        "/manager/user-accounts",
        "/manager/register-candidate", // This will match both /manager/register-candidate and /manager/register-candidate/:jobId
        "/manager/peer-assigned-profiles",
        "/manager/profile-transfer",
        "/manager/job-listing",
        "/manager/job-assignments",
        "/calendar",
        "/help-and-support",
        "/change-password",
        "/manager/talent-pool",
        "/register-candidate"
      ];
      const isOnSpecificRoute = specificRoutes.some(route => currentPath.startsWith(route));

      return !isOnSpecificRoute;
    }

    // Special handling for Available Candidates when in register-candidate mode
    if (menuUrl === "/manager/talent-pool" && currentPath === "/register-candidate") {
      // Check if we're in available candidates mode by looking at location state
      const locationState = location.state as any;
      if (locationState && locationState.isTalentPoolMode) {
        return true;
      }
    }

    // For other items, check if current path starts with the menu URL
    if (currentPath.startsWith(menuUrl)) return true;

    return false;
  };


  return (
    <Sidebar className="bg-[var(--sidebar)] text-[var(--sidebar-foreground)] w-52 border-r border-[var(--sidebar-border)]">
      {/* Profile Avatar Section */}
      <ProfileAvatar />

      <SidebarContent>
        {menuSections.map((section, sectionIndex) => (
          <SidebarGroup key={sectionIndex}>
            {section.title && (
              <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-1 py-0.5 ml-2 -mt-3">
                {section.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent className="space-y-0.5">
              <SidebarMenu className="space-y-0.5">
                {section.items.map((item, itemIndex) => (
                  <SidebarMenuItem key={itemIndex} className="ml-2">
                    {item.title === "Logout" ? (
                      <SidebarMenuButton
                        onClick={handleLogoutClick}
                        className="w-full px-1 py-0.5 cursor-pointer"
                      >
                        <LogOut className="h-4 w-4 sidebar-icon-system" />
                        <span>Logout</span>
                      </SidebarMenuButton>
                    ) : (
                      <SidebarMenuButton
                        asChild
                        isActive={isMenuItemActive(item.url)}
                        className="w-full px-1 py-1"
                      >
                        <Link to={item.url}>
                          <item.icon className={`h-4 w-4 ${item.title === "Dashboard" ? "sidebar-icon-dashboard" :
                            item.title === "Candidate Repository" || item.title === "Available Candidates" || item.title === "Register Candidate" || item.title === "Peer Assigned Profiles" || item.title === "Profile Transfer" ? "sidebar-icon-candidates" :
                              item.title === "Job Listing" || item.title === "Job Assignments" ? "sidebar-icon-recruitment" :
                                item.title === "Analytics" || item.title === "Calendar" || item.title === "Help and Support" ? "sidebar-icon-general" :
                                  item.title === "User Accounts" || item.title === "Change Password" ? "sidebar-icon-user" :
                                    item.title === "Logout" ? "sidebar-icon-system" : ""
                            }`} />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    )}
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      {LogoutDialog()}
    </Sidebar>
  );
}
